"""
AI Model management system for Reverie CLI.

This module provides comprehensive model management capabilities including:
- Multi-backend support (Transformers, vLLM, GGUF, TensorFlow)
- Model loading, unloading, and caching
- Performance optimization and device management
- Model information and metadata handling
"""

from reverie_cli.models.manager import ModelManager
from reverie_cli.models.backends import ModelBackend
from reverie_cli.models.info import ModelInfo

__all__ = [
    "ModelManager",
    "ModelBackend", 
    "ModelInfo",
]
