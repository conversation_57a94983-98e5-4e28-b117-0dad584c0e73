"""
Code execution tools.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, List

from reverie_cli.tools.base import BaseTool, ToolResult, ToolParameter, ToolCategory


class PythonExecutorTool(BaseTool):
    """Tool for executing Python code."""
    
    @property
    def name(self) -> str:
        return "execute_python"
    
    @property
    def description(self) -> str:
        return "Execute Python code and return the output"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.CODE
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="code",
                type="string",
                description="Python code to execute",
                required=True
            ),
            ToolParameter(
                name="timeout",
                type="number",
                description="Execution timeout in seconds",
                required=False,
                default=30
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute Python code."""
        code = parameters["code"]
        timeout = parameters.get("timeout", 30)
        
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Execute the code
                process = await asyncio.create_subprocess_exec(
                    'python', temp_file,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=timeout
                )
                
                stdout_text = stdout.decode('utf-8') if stdout else ""
                stderr_text = stderr.decode('utf-8') if stderr else ""
                
                if process.returncode == 0:
                    return ToolResult(
                        success=True,
                        result=stdout_text,
                        message="Python code executed successfully",
                        metadata={
                            "return_code": process.returncode,
                            "stderr": stderr_text
                        }
                    )
                else:
                    return ToolResult(
                        success=False,
                        error=stderr_text,
                        message=f"Python code execution failed with return code {process.returncode}",
                        metadata={
                            "return_code": process.returncode,
                            "stdout": stdout_text
                        }
                    )
                    
            finally:
                # Clean up temporary file
                Path(temp_file).unlink(missing_ok=True)
                
        except asyncio.TimeoutError:
            return ToolResult(
                success=False,
                error="Execution timeout",
                message=f"Python code execution timed out after {timeout} seconds"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message="Failed to execute Python code"
            )


class ShellExecutorTool(BaseTool):
    """Tool for executing shell commands."""
    
    @property
    def name(self) -> str:
        return "execute_shell"
    
    @property
    def description(self) -> str:
        return "Execute shell commands and return the output"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.SYSTEM
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="command",
                type="string",
                description="Shell command to execute",
                required=True
            ),
            ToolParameter(
                name="working_directory",
                type="string",
                description="Working directory for the command",
                required=False,
                default="."
            ),
            ToolParameter(
                name="timeout",
                type="number",
                description="Execution timeout in seconds",
                required=False,
                default=30
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute shell command."""
        command = parameters["command"]
        working_dir = parameters.get("working_directory", ".")
        timeout = parameters.get("timeout", 30)
        
        try:
            # Execute the command
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=working_dir
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )
            
            stdout_text = stdout.decode('utf-8') if stdout else ""
            stderr_text = stderr.decode('utf-8') if stderr else ""
            
            if process.returncode == 0:
                return ToolResult(
                    success=True,
                    result=stdout_text,
                    message="Shell command executed successfully",
                    metadata={
                        "return_code": process.returncode,
                        "stderr": stderr_text,
                        "command": command,
                        "working_directory": working_dir
                    }
                )
            else:
                return ToolResult(
                    success=False,
                    error=stderr_text,
                    message=f"Shell command failed with return code {process.returncode}",
                    metadata={
                        "return_code": process.returncode,
                        "stdout": stdout_text,
                        "command": command,
                        "working_directory": working_dir
                    }
                )
                
        except asyncio.TimeoutError:
            return ToolResult(
                success=False,
                error="Execution timeout",
                message=f"Shell command timed out after {timeout} seconds"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message="Failed to execute shell command"
            )
