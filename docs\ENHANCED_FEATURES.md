# 🚀 Reverie CLI - Enhanced Features Documentation

## Overview

Reverie CLI has been significantly enhanced with AI-native capabilities similar to Augment and Claude Code. This document outlines the new features, engines, and capabilities that make Reverie CLI a powerful AI-driven development environment.

## 🎯 Dual-Mode Operation

Reverie CLI now operates in **dual-mode**, providing both direct AI assistance and API service capabilities simultaneously:

### 🤖 AI Coder Mode
- **Direct AI Assistance**: Natural language interaction for coding tasks
- **Intelligent Code Generation**: Complete, production-ready code creation
- **Context-Aware Responses**: Understanding of project structure and requirements
- **Real-time Learning**: Adapts to user preferences and patterns

### ⚙️ API Service Mode
- **REST API Endpoints**: Standard API access for external integrations
- **Server Management**: Monitor and control the development server
- **Model Management**: Load, switch, and optimize AI models
- **System Monitoring**: Real-time performance and health tracking

## 🧠 Enhanced AI Engines

### 🌐 Web Engine
Advanced web search and content processing capabilities:

#### One-Line Operations
```python
# Smart web search with AI analysis
web_search(query, type="smart", max_results=10)

# Intelligent content extraction
web_extract(url, type="smart")

# Real-time monitoring
web_monitor(url, interval="1h")

# Latest news and updates
web_news(topic, timeframe="24h")

# Documentation search
web_docs(technology, version="latest")
```

#### Features
- **Multi-Engine Search**: Google, Bing, DuckDuckGo, Searx with automatic fallback
- **AI-Powered Analysis**: Content summarization and relevance scoring
- **Persistent Caching**: SQLite-based caching for performance
- **Content Classification**: Automatic content type detection and analysis
- **Real-time Information**: Access to current web content and documentation

### 🔍 Context Engine
Deep codebase understanding and analysis:

#### One-Line Operations
```python
# Comprehensive code analysis
code_analyze(path, type="comprehensive", include_ai=True)

# Symbol and relationship mapping
code_symbols(query, scope="project")
code_relationships(symbol, depth=3)

# Quality and security assessment
code_quality(path, focus="all")
code_security(path, level="comprehensive")

# Project overview
project_overview(path, include_metrics=True)
```

#### Features
- **Multi-Language Support**: Python, JavaScript, TypeScript, Java, C++, Go, Rust, Ruby, PHP, C#
- **Framework Detection**: Automatic detection of frameworks and technologies
- **Dependency Analysis**: Complete dependency mapping and relationship tracking
- **Code Quality Metrics**: Complexity, maintainability, and quality scoring
- **AI-Powered Insights**: Intelligent suggestions and pattern recognition
- **Persistent Storage**: SQLite-based storage for analysis results

### 🧠 Memory Engine
Persistent learning and intelligent search:

#### One-Line Operations
```python
# Smart memory storage
remember(content, type="auto", importance=0.7, tags=None)

# Intelligent search
recall(query, type="smart", max_results=10)

# Pattern learning
learn_pattern(pattern, context, confidence=0.8)

# Memory insights
memory_insights(timeframe="7d")

# Conversation context
conversation_context(limit=10)
```

#### Features
- **Persistent Storage**: SQLite-based memory with intelligent indexing
- **Semantic Search**: Vector-based similarity search for content
- **Learning Capabilities**: Pattern recognition and user preference learning
- **Context Awareness**: Conversation and project context understanding
- **Auto-Categorization**: Intelligent content classification and tagging
- **Memory Management**: Automatic cleanup and importance-based retention

## 🎪 Enhanced Console Interface

### Dual-Mode Console
The enhanced console supports both modes simultaneously:

```bash
# AI Coder Mode - Natural language
🤖 [general] reverie> Create a REST API for user management with authentication

# API Service Mode - Direct commands
⚙️ [general] reverie> server status
⚙️ [general] reverie> models list
```

### Smart Command Processing
- **Natural Language Understanding**: Parse intent from conversational input
- **Context-Aware Completion**: Intelligent auto-completion based on current context
- **Command Suggestions**: AI-powered command recommendations
- **Learning Interface**: Adapts to user patterns and preferences

### Available Commands

#### AI Coder Mode Commands
- `ai <request>` - Direct AI assistance
- `code <create|edit|analyze|review|test>` - AI coding tools
- `analyze <path>` - Code analysis and insights
- `review <path>` - Code review and suggestions
- `refactor <path>` - Refactoring assistance

#### Enhanced Tool Commands
- `web <query>` - Web search and content retrieval
- `search <query>` - Intelligent memory search
- `remember <content>` - Store information in memory
- `context <analyze|set> [path]` - Context management

#### System Commands
- `status` - Show system status
- `mode <ai|api>` - Toggle modes
- `help` - Show all commands
- `clear` - Clear screen

## 🌟 Enhanced API Endpoints

### Chat API with Engine Integration

#### Enhanced Chat Completion
```http
POST /v1/chat/enhanced
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "Help me optimize this Python function"}
  ],
  "use_web_search": true,
  "use_memory": true,
  "use_context_analysis": true,
  "project_path": "./my-project",
  "context_type": "coding",
  "remember_conversation": true
}
```

#### Smart Code Assistance
```http
POST /v1/chat/smart-code
Content-Type: application/json

{
  "task": "Create a user authentication system",
  "code_context": "FastAPI application with SQLAlchemy",
  "project_path": "./api-project"
}
```

### Enhanced Agent API

#### Execute Agent Task
```http
POST /v1/agent/execute
Content-Type: application/json

{
  "task_type": "code",
  "description": "Create a REST API endpoint for user registration",
  "context": {
    "framework": "FastAPI",
    "database": "PostgreSQL"
  },
  "project_path": "./my-api",
  "use_engines": ["web", "context", "memory"],
  "priority": "normal",
  "remember_result": true
}
```

#### Smart Assistance
```http
POST /v1/agent/smart-assist
Content-Type: application/json

{
  "request": "How do I implement JWT authentication in FastAPI?",
  "context": {
    "project_path": "./my-api",
    "current_framework": "FastAPI"
  }
}
```

## 🔧 System Prompt Enhancements

### One-Line Complex Operations
The enhanced system prompts include powerful one-line functions:

```python
# Composite operations
smart_solution(problem) = web_search(problem) + recall(similar_issues) + code_analyze(context) + generate_solution()

project_health(path) = project_overview(path) + dependency_audit(path) + performance_analyze(path) + remember(health_report)

full_analysis(path) = code_analyze(path) + code_quality(path) + security_audit(path) + remember(results)
```

### Enhanced Capabilities
- **Multi-Engine Integration**: Seamless combination of Web, Context, and Memory engines
- **Smart Defaults**: Intelligent parameter defaults for optimal results
- **Auto-Enhancement**: Automatic AI analysis inclusion when beneficial
- **Learning Integration**: Continuous learning from interactions and patterns

## 📊 Performance and Monitoring

### Caching and Optimization
- **Persistent Caching**: SQLite-based caching for web content and analysis results
- **Intelligent Expiration**: Smart cache management with TTL and importance-based retention
- **Background Processing**: Asynchronous operations for better performance
- **Memory Management**: Efficient memory usage with automatic cleanup

### Monitoring and Health
- **Real-time Status**: Live monitoring of all engines and services
- **Performance Metrics**: Detailed performance tracking and optimization
- **Error Handling**: Comprehensive error handling with graceful degradation
- **Logging**: Detailed logging for debugging and monitoring

## 🚀 Getting Started

### Installation and Setup
1. Install enhanced dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Start the enhanced server:
   ```bash
   python -m reverie_cli.main --enhanced
   ```

3. Access the dual-mode console:
   ```bash
   python -m reverie_cli.cli --console
   ```

### Configuration
The enhanced features can be configured through environment variables or configuration files:

```yaml
# config.yaml
engines:
  web:
    enabled: true
    cache_ttl: 3600
    max_results: 10
  context:
    enabled: true
    analysis_depth: "comprehensive"
    cache_results: true
  memory:
    enabled: true
    max_items: 100000
    learning_rate: 0.1

console:
  ai_coder_mode: true
  api_service_mode: true
  auto_complete: true
  learning_enabled: true
```

## 🎯 Use Cases

### Development Workflow
1. **Project Analysis**: `code_analyze("./project", type="comprehensive")`
2. **Research Solutions**: `web_search("best practices for authentication")`
3. **Generate Code**: Natural language requests for code generation
4. **Review and Optimize**: `code_review("./src") + performance_analyze("./src")`
5. **Learn and Remember**: Automatic learning from all interactions

### AI-Powered Assistance
- **Intelligent Debugging**: Combine error analysis with web search for solutions
- **Code Generation**: Context-aware code creation with best practices
- **Documentation**: Automatic documentation generation with examples
- **Learning**: Continuous improvement through interaction patterns

The enhanced Reverie CLI provides a comprehensive AI-native development environment that combines the power of multiple AI engines with intelligent automation and learning capabilities.
