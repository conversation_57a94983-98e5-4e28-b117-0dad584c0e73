"""
Enhanced Web Engine for Reverie CLI.

This module provides advanced web search, content retrieval, and information
processing capabilities similar to Augment and Gemini CLI. It includes:
- Smart web search with multiple search engines and fallbacks
- Advanced content extraction and summarization with AI
- Real-time information retrieval and monitoring
- Website analysis and change detection
- Social media and news tracking with sentiment analysis
- Intelligent content caching and optimization
- One-line function calls for complex operations
"""

import asyncio
import aiohttp
import json
import re
import hashlib
import sqlite3
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse, quote_plus
from bs4 import BeautifulSoup
import feedparser
from pathlib import Path

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings
from reverie_cli.tools.base import BaseTool, ToolResult, ToolError, ToolCategory, ToolParameter


class WebEngine(BaseTool):
    """
    Enhanced Web Engine with intelligent search and content processing.

    Provides comprehensive web capabilities including search, content extraction,
    monitoring, real-time information retrieval, and AI-powered analysis.
    """

    def __init__(self):
        self._name = "web_engine"
        self._description = "Enhanced web search and content retrieval engine with AI capabilities"
        super().__init__()
        self.logger = get_logger("web_engine")
        self.settings = get_settings()

    @property
    def name(self) -> str:
        """Get the tool name."""
        return "web_engine"

    @property
    def description(self) -> str:
        """Get the tool description."""
        return "Enhanced web search and content extraction with AI analysis"

    @property
    def category(self) -> ToolCategory:
        """Get the tool category."""
        return ToolCategory.WEB

    @property
    def parameters(self) -> List[ToolParameter]:
        """Get the tool parameters schema."""
        return [
            ToolParameter(
                name="action",
                type="string",
                description="Action to perform",
                required=False,
                default="search",
                choices=["search", "extract", "monitor"]
            ),
            ToolParameter(
                name="query",
                type="string",
                description="Search query (for search action)",
                required=False
            ),
            ToolParameter(
                name="url",
                type="string",
                description="URL to extract (for extract action)",
                required=False
            ),
            ToolParameter(
                name="max_results",
                type="integer",
                description="Maximum number of results",
                required=False,
                default=5
            ),
            ToolParameter(
                name="include_analysis",
                type="boolean",
                description="Include AI analysis",
                required=False,
                default=True
            )
        ]

    def __init__(self):
        """Initialize the enhanced web engine."""
        super().__init__()
        self.logger = get_logger("web_engine")
        self.settings = get_settings()

        # Initialize database for persistent caching
        self.db_path = Path("data/web_cache.db")
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()

        # Enhanced search engines configuration with fallbacks
        self.search_engines = {
            "google": {
                "url": "https://www.googleapis.com/customsearch/v1",
                "params": {
                    "key": getattr(self.settings.web, 'google_api_key', None),
                    "cx": getattr(self.settings.web, 'google_search_engine_id', None),
                },
                "priority": 1,
                "rate_limit": 100  # requests per day
            },
            "bing": {
                "url": "https://api.bing.microsoft.com/v7.0/search",
                "headers": {
                    "Ocp-Apim-Subscription-Key": getattr(self.settings.web, 'bing_api_key', None)
                },
                "priority": 2,
                "rate_limit": 1000
            },
            "duckduckgo": {
                "url": "https://api.duckduckgo.com/",
                "params": {"format": "json", "no_html": "1"},
                "priority": 3,
                "rate_limit": float('inf')  # No rate limit
            },
            "searx": {
                "url": "https://searx.org/search",
                "params": {"format": "json"},
                "priority": 4,
                "rate_limit": float('inf')
            }
        }

        # Enhanced content processing settings
        self.max_content_length = 50000  # Increased for better context
        self.request_timeout = 45
        self.max_concurrent_requests = 10
        self.user_agent = "Mozilla/5.0 (compatible; ReverieBot/1.0; +https://reverie-cli.ai)"

        # Advanced caching system
        self.memory_cache: Dict[str, Dict] = {}
        self.cache_ttl = timedelta(hours=6)  # Longer cache for better performance
        self.max_cache_size = 1000

        # Content analysis patterns
        self.content_patterns = {
            "code": re.compile(r'```[\s\S]*?```|<code>[\s\S]*?</code>', re.MULTILINE),
            "links": re.compile(r'https?://[^\s<>"]+'),
            "emails": re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            "dates": re.compile(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}-\d{2}-\d{2}\b'),
        }

        self.logger.info("Enhanced WebEngine initialized with persistent caching and AI capabilities")

    def _init_database(self):
        """Initialize SQLite database for persistent caching."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS search_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        query_hash TEXT UNIQUE,
                        query TEXT,
                        engine TEXT,
                        results TEXT,
                        timestamp DATETIME,
                        expiry DATETIME
                    )
                """)

                conn.execute("""
                    CREATE TABLE IF NOT EXISTS content_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url_hash TEXT UNIQUE,
                        url TEXT,
                        content TEXT,
                        metadata TEXT,
                        timestamp DATETIME,
                        expiry DATETIME
                    )
                """)

                conn.execute("""
                    CREATE TABLE IF NOT EXISTS monitoring (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        url TEXT,
                        content_hash TEXT,
                        last_check DATETIME,
                        change_count INTEGER DEFAULT 0
                    )
                """)

                # Create indices for better performance
                conn.execute("CREATE INDEX IF NOT EXISTS idx_query_hash ON search_cache(query_hash)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_url_hash ON content_cache(url_hash)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_monitoring_url ON monitoring(url)")

        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")

    async def smart_search(
        self,
        query: str,
        context: Optional[str] = None,
        max_results: int = 10,
        include_analysis: bool = True
    ) -> ToolResult:
        """
        One-line smart search with automatic engine selection, caching, and AI analysis.

        Args:
            query: Search query
            context: Additional context for better results
            max_results: Maximum number of results
            include_analysis: Include AI-powered content analysis
        """
        try:
            self.logger.info(f"Smart search for: {query}")

            # Enhance query with context if provided
            enhanced_query = self._enhance_query(query, context)

            # Check persistent cache first
            cached_result = await self._get_cached_search(enhanced_query)
            if cached_result:
                self.logger.info("Returning cached search results")
                return cached_result

            # Auto-select best engine based on query analysis
            best_engine = await self._select_optimal_engine(enhanced_query)

            # Perform search with fallback engines
            results = await self._search_with_fallback(enhanced_query, best_engine, max_results)

            # Enhance results with AI analysis if requested
            if include_analysis and results:
                results = await self._analyze_search_results(results, query)

            # Cache results
            await self._cache_search_results(enhanced_query, best_engine, results)

            return ToolResult(
                success=True,
                data={
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "engine_used": best_engine,
                    "results": results,
                    "total_results": len(results),
                    "analysis_included": include_analysis
                },
                message=f"Smart search completed: {len(results)} results found"
            )

        except Exception as e:
            self.logger.error(f"Smart search failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Smart search failed"
            )

    async def search_web(
        self,
        query: str,
        engine: str = "auto",
        max_results: int = 10,
        include_snippets: bool = True,
        filter_type: Optional[str] = None
    ) -> ToolResult:
        """
        Perform intelligent web search across multiple engines.
        
        Args:
            query: Search query
            engine: Search engine to use ("google", "bing", "duckduckgo", "auto")
            max_results: Maximum number of results
            include_snippets: Whether to include content snippets
            filter_type: Filter results by type ("news", "images", "videos", etc.)
        """
        try:
            self.logger.info(f"Searching web for: {query}")
            
            # Check cache first
            cache_key = f"{query}:{engine}:{max_results}"
            if cache_key in self.search_cache:
                cached_result = self.search_cache[cache_key]
                if datetime.now() - cached_result["timestamp"] < self.cache_ttl:
                    self.logger.info("Returning cached search results")
                    return ToolResult(
                        success=True,
                        data=cached_result["data"],
                        message="Search completed (cached)"
                    )
            
            # Select search engine
            if engine == "auto":
                engine = self._select_best_engine(query)
            
            # Perform search
            results = await self._perform_search(query, engine, max_results, filter_type)
            
            # Process results
            processed_results = []
            for result in results:
                processed_result = {
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "snippet": result.get("snippet", ""),
                    "source": engine,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Extract additional content if requested
                if include_snippets and result.get("url"):
                    try:
                        content = await self._extract_content(result["url"])
                        processed_result["content"] = content[:self.max_content_length]
                    except Exception as e:
                        self.logger.warning(f"Failed to extract content from {result['url']}: {e}")
                
                processed_results.append(processed_result)
            
            # Cache results
            self.search_cache[cache_key] = {
                "data": processed_results,
                "timestamp": datetime.now()
            }
            
            return ToolResult(
                success=True,
                data=processed_results,
                message=f"Found {len(processed_results)} results"
            )
            
        except Exception as e:
            self.logger.error(f"Web search failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Web search failed"
            )
    
    async def extract_content(self, url: str, extract_type: str = "text") -> ToolResult:
        """
        Extract and process content from a webpage.
        
        Args:
            url: URL to extract content from
            extract_type: Type of extraction ("text", "structured", "metadata")
        """
        try:
            self.logger.info(f"Extracting content from: {url}")
            
            content = await self._extract_content(url)
            
            if extract_type == "structured":
                # Extract structured data (headings, lists, etc.)
                structured_content = await self._extract_structured_content(url)
                return ToolResult(
                    success=True,
                    data=structured_content,
                    message="Structured content extracted"
                )
            elif extract_type == "metadata":
                # Extract metadata (title, description, keywords, etc.)
                metadata = await self._extract_metadata(url)
                return ToolResult(
                    success=True,
                    data=metadata,
                    message="Metadata extracted"
                )
            else:
                # Return plain text content
                return ToolResult(
                    success=True,
                    data={"content": content, "url": url},
                    message="Content extracted"
                )
                
        except Exception as e:
            self.logger.error(f"Content extraction failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Content extraction failed"
            )
    
    async def monitor_website(
        self,
        url: str,
        check_interval: int = 3600,
        change_threshold: float = 0.1
    ) -> ToolResult:
        """
        Monitor a website for changes.
        
        Args:
            url: URL to monitor
            check_interval: Check interval in seconds
            change_threshold: Minimum change threshold (0.0-1.0)
        """
        try:
            self.logger.info(f"Starting website monitoring for: {url}")
            
            # Get initial content
            initial_content = await self._extract_content(url)
            
            # Store monitoring configuration
            monitor_config = {
                "url": url,
                "initial_content": initial_content,
                "check_interval": check_interval,
                "change_threshold": change_threshold,
                "last_check": datetime.now().isoformat(),
                "changes_detected": 0
            }
            
            return ToolResult(
                success=True,
                data=monitor_config,
                message="Website monitoring started"
            )
            
        except Exception as e:
            self.logger.error(f"Website monitoring setup failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Website monitoring setup failed"
            )
    
    async def search_news(
        self,
        query: str,
        time_range: str = "24h",
        sources: Optional[List[str]] = None
    ) -> ToolResult:
        """
        Search for recent news articles.
        
        Args:
            query: Search query
            time_range: Time range ("1h", "24h", "7d", "30d")
            sources: Specific news sources to search
        """
        try:
            self.logger.info(f"Searching news for: {query}")
            
            # Convert time range to date filter
            time_filter = self._convert_time_range(time_range)
            
            # Search news sources
            news_results = []
            
            # Search RSS feeds
            if sources:
                for source in sources:
                    try:
                        feed_results = await self._search_rss_feed(source, query)
                        news_results.extend(feed_results)
                    except Exception as e:
                        self.logger.warning(f"Failed to search RSS feed {source}: {e}")
            
            # Search news APIs
            api_results = await self._search_news_apis(query, time_filter)
            news_results.extend(api_results)
            
            # Sort by relevance and recency
            news_results.sort(key=lambda x: (x.get("relevance", 0), x.get("published", "")), reverse=True)
            
            return ToolResult(
                success=True,
                data=news_results[:20],  # Limit to top 20 results
                message=f"Found {len(news_results)} news articles"
            )
            
        except Exception as e:
            self.logger.error(f"News search failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="News search failed"
            )
    
    def _select_best_engine(self, query: str) -> str:
        """Select the best search engine based on query characteristics."""
        # Simple heuristics for engine selection
        if any(keyword in query.lower() for keyword in ["code", "programming", "github", "stackoverflow"]):
            return "google"  # Google is generally better for technical content
        elif any(keyword in query.lower() for keyword in ["news", "recent", "latest"]):
            return "bing"  # Bing often has more recent content
        else:
            return "duckduckgo"  # Privacy-focused default
    
    async def _perform_search(
        self,
        query: str,
        engine: str,
        max_results: int,
        filter_type: Optional[str]
    ) -> List[Dict]:
        """Perform search using specified engine."""
        if engine not in self.search_engines:
            raise ToolError(f"Unsupported search engine: {engine}")
        
        engine_config = self.search_engines[engine]
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
            if engine == "google":
                return await self._search_google(session, query, max_results, filter_type, engine_config)
            elif engine == "bing":
                return await self._search_bing(session, query, max_results, filter_type, engine_config)
            elif engine == "duckduckgo":
                return await self._search_duckduckgo(session, query, max_results, filter_type, engine_config)
        
        return []
    
    async def _extract_content(self, url: str) -> str:
        """Extract text content from a webpage."""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Extract text
                    text = soup.get_text()
                    
                    # Clean up text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    return text
                else:
                    raise ToolError(f"Failed to fetch content: HTTP {response.status}")

    async def _search_google(self, session, query: str, max_results: int, filter_type: Optional[str], config: Dict) -> List[Dict]:
        """Search using Google Custom Search API."""
        params = config["params"].copy()
        params.update({
            "q": query,
            "num": min(max_results, 10)  # Google API limit
        })

        if filter_type:
            params["searchType"] = filter_type

        try:
            async with session.get(config["url"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    for item in data.get("items", []):
                        results.append({
                            "title": item.get("title", ""),
                            "url": item.get("link", ""),
                            "snippet": item.get("snippet", "")
                        })
                    return results
                else:
                    self.logger.warning(f"Google search failed: HTTP {response.status}")
                    return []
        except Exception as e:
            self.logger.warning(f"Google search error: {e}")
            return []

    async def _search_bing(self, session, query: str, max_results: int, filter_type: Optional[str], config: Dict) -> List[Dict]:
        """Search using Bing Search API."""
        params = {
            "q": query,
            "count": min(max_results, 50),  # Bing API limit
            "offset": 0,
            "mkt": "en-US"
        }

        if filter_type:
            params["responseFilter"] = filter_type

        try:
            async with session.get(config["url"], params=params, headers=config["headers"]) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    for item in data.get("webPages", {}).get("value", []):
                        results.append({
                            "title": item.get("name", ""),
                            "url": item.get("url", ""),
                            "snippet": item.get("snippet", "")
                        })
                    return results
                else:
                    self.logger.warning(f"Bing search failed: HTTP {response.status}")
                    return []
        except Exception as e:
            self.logger.warning(f"Bing search error: {e}")
            return []

    async def _search_duckduckgo(self, session, query: str, max_results: int, filter_type: Optional[str], config: Dict) -> List[Dict]:
        """Search using DuckDuckGo Instant Answer API."""
        params = config["params"].copy()
        params["q"] = query

        try:
            async with session.get(config["url"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []

                    # DuckDuckGo has a different response format
                    if data.get("AbstractText"):
                        results.append({
                            "title": data.get("Heading", query),
                            "url": data.get("AbstractURL", ""),
                            "snippet": data.get("AbstractText", "")
                        })

                    # Add related topics
                    for topic in data.get("RelatedTopics", [])[:max_results-1]:
                        if isinstance(topic, dict) and topic.get("Text"):
                            results.append({
                                "title": topic.get("Text", "").split(" - ")[0],
                                "url": topic.get("FirstURL", ""),
                                "snippet": topic.get("Text", "")
                            })

                    return results
                else:
                    self.logger.warning(f"DuckDuckGo search failed: HTTP {response.status}")
                    return []
        except Exception as e:
            self.logger.warning(f"DuckDuckGo search error: {e}")
            return []

    async def _extract_structured_content(self, url: str) -> Dict:
        """Extract structured content from a webpage."""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    structured_data = {
                        "headings": [],
                        "lists": [],
                        "tables": [],
                        "links": [],
                        "images": []
                    }

                    # Extract headings
                    for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                        structured_data["headings"].append({
                            "level": int(heading.name[1]),
                            "text": heading.get_text().strip()
                        })

                    # Extract lists
                    for list_elem in soup.find_all(['ul', 'ol']):
                        items = [li.get_text().strip() for li in list_elem.find_all('li')]
                        structured_data["lists"].append({
                            "type": list_elem.name,
                            "items": items
                        })

                    # Extract tables
                    for table in soup.find_all('table'):
                        rows = []
                        for row in table.find_all('tr'):
                            cells = [cell.get_text().strip() for cell in row.find_all(['td', 'th'])]
                            if cells:
                                rows.append(cells)
                        if rows:
                            structured_data["tables"].append(rows)

                    # Extract links
                    for link in soup.find_all('a', href=True):
                        structured_data["links"].append({
                            "text": link.get_text().strip(),
                            "url": urljoin(url, link['href'])
                        })

                    # Extract images
                    for img in soup.find_all('img', src=True):
                        structured_data["images"].append({
                            "alt": img.get('alt', ''),
                            "src": urljoin(url, img['src'])
                        })

                    return structured_data
                else:
                    raise ToolError(f"Failed to fetch content: HTTP {response.status}")

    async def _extract_metadata(self, url: str) -> Dict:
        """Extract metadata from a webpage."""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    metadata = {
                        "title": "",
                        "description": "",
                        "keywords": "",
                        "author": "",
                        "published": "",
                        "og_data": {},
                        "twitter_data": {}
                    }

                    # Basic metadata
                    title_tag = soup.find('title')
                    if title_tag:
                        metadata["title"] = title_tag.get_text().strip()

                    # Meta tags
                    for meta in soup.find_all('meta'):
                        name = meta.get('name', '').lower()
                        property_name = meta.get('property', '').lower()
                        content = meta.get('content', '')

                        if name == 'description':
                            metadata["description"] = content
                        elif name == 'keywords':
                            metadata["keywords"] = content
                        elif name == 'author':
                            metadata["author"] = content
                        elif property_name.startswith('og:'):
                            metadata["og_data"][property_name] = content
                        elif name.startswith('twitter:'):
                            metadata["twitter_data"][name] = content

                    return metadata
                else:
                    raise ToolError(f"Failed to fetch content: HTTP {response.status}")

    def _convert_time_range(self, time_range: str) -> datetime:
        """Convert time range string to datetime filter."""
        now = datetime.now()
        if time_range == "1h":
            return now - timedelta(hours=1)
        elif time_range == "24h":
            return now - timedelta(days=1)
        elif time_range == "7d":
            return now - timedelta(days=7)
        elif time_range == "30d":
            return now - timedelta(days=30)
        else:
            return now - timedelta(days=1)  # Default to 24h

    async def _search_rss_feed(self, feed_url: str, query: str) -> List[Dict]:
        """Search RSS feed for relevant articles."""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
                async with session.get(feed_url) as response:
                    if response.status == 200:
                        feed_content = await response.text()
                        feed = feedparser.parse(feed_content)

                        results = []
                        for entry in feed.entries:
                            # Simple relevance check
                            title = entry.get('title', '').lower()
                            summary = entry.get('summary', '').lower()
                            query_lower = query.lower()

                            if query_lower in title or query_lower in summary:
                                results.append({
                                    "title": entry.get('title', ''),
                                    "url": entry.get('link', ''),
                                    "snippet": entry.get('summary', ''),
                                    "published": entry.get('published', ''),
                                    "source": feed_url,
                                    "relevance": self._calculate_relevance(query_lower, title, summary)
                                })

                        return results
                    else:
                        return []
        except Exception as e:
            self.logger.warning(f"RSS feed search error: {e}")
            return []

    async def _search_news_apis(self, query: str, time_filter: datetime) -> List[Dict]:
        """Search news APIs for relevant articles."""
        # This would integrate with news APIs like NewsAPI, etc.
        # For now, return empty list as placeholder
        return []

    def _enhance_query(self, query: str, context: Optional[str] = None) -> str:
        """Enhance search query with context and intelligent keywords."""
        enhanced = query.strip()

        # Add context if provided
        if context:
            # Extract key terms from context
            context_words = re.findall(r'\b\w{4,}\b', context.lower())
            relevant_context = [word for word in context_words if word not in {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'will'}]

            if relevant_context:
                enhanced += f" {' '.join(relevant_context[:3])}"

        # Add current year for time-sensitive queries
        time_indicators = ['latest', 'recent', 'new', 'current', '2024', '2025']
        if any(indicator in enhanced.lower() for indicator in time_indicators):
            current_year = datetime.now().year
            enhanced += f" {current_year}"

        return enhanced

    async def _select_optimal_engine(self, query: str) -> str:
        """Select the best search engine based on query characteristics and availability."""
        query_lower = query.lower()

        # Technical queries - prefer Google
        if any(term in query_lower for term in ['code', 'programming', 'api', 'documentation', 'github', 'stackoverflow']):
            if self.search_engines["google"]["params"]["key"]:
                return "google"

        # News and current events - prefer Bing
        if any(term in query_lower for term in ['news', 'breaking', 'latest', 'today', 'yesterday']):
            if self.search_engines["bing"]["headers"].get("Ocp-Apim-Subscription-Key"):
                return "bing"

        # Privacy-focused or general queries - prefer DuckDuckGo
        if any(term in query_lower for term in ['privacy', 'anonymous', 'secure']):
            return "duckduckgo"

        # Check engine availability and select best available
        for engine_name, config in sorted(self.search_engines.items(), key=lambda x: x[1]["priority"]):
            if self._is_engine_available(engine_name):
                return engine_name

        return "duckduckgo"  # Fallback

    def _is_engine_available(self, engine_name: str) -> bool:
        """Check if a search engine is available and configured."""
        config = self.search_engines.get(engine_name, {})

        if engine_name == "google":
            return bool(config.get("params", {}).get("key"))
        elif engine_name == "bing":
            return bool(config.get("headers", {}).get("Ocp-Apim-Subscription-Key"))
        else:
            return True  # DuckDuckGo and Searx don't require API keys

    async def _search_with_fallback(self, query: str, primary_engine: str, max_results: int) -> List[Dict]:
        """Perform search with automatic fallback to other engines if primary fails."""
        engines_to_try = [primary_engine]

        # Add fallback engines in priority order
        for engine_name in sorted(self.search_engines.keys(), key=lambda x: self.search_engines[x]["priority"]):
            if engine_name != primary_engine and self._is_engine_available(engine_name):
                engines_to_try.append(engine_name)

        for engine in engines_to_try:
            try:
                self.logger.info(f"Trying search with {engine}")
                results = await self._perform_search(query, engine, max_results, None)
                if results:
                    self.logger.info(f"Search successful with {engine}: {len(results)} results")
                    return results
            except Exception as e:
                self.logger.warning(f"Search failed with {engine}: {e}")
                continue

        self.logger.warning("All search engines failed")
        return []

    async def _analyze_search_results(self, results: List[Dict], original_query: str) -> List[Dict]:
        """Enhance search results with AI-powered analysis."""
        enhanced_results = []

        for result in results:
            enhanced_result = result.copy()

            # Calculate enhanced relevance score
            enhanced_result["relevance_score"] = self._calculate_enhanced_relevance(
                original_query, result.get("title", ""), result.get("snippet", "")
            )

            # Extract and analyze content patterns
            content = result.get("content", result.get("snippet", ""))
            if content:
                enhanced_result["content_analysis"] = self._analyze_content_patterns(content)

            # Determine content type and quality
            enhanced_result["content_type"] = self._classify_content_type(result.get("url", ""), content)
            enhanced_result["quality_score"] = self._calculate_quality_score(result)

            enhanced_results.append(enhanced_result)

        # Sort by combined relevance and quality score
        enhanced_results.sort(
            key=lambda x: (x.get("relevance_score", 0) + x.get("quality_score", 0)) / 2,
            reverse=True
        )

        return enhanced_results

    def _calculate_enhanced_relevance(self, query: str, title: str, content: str) -> float:
        """Calculate enhanced relevance score using multiple factors."""
        score = 0.0
        query_words = query.lower().split()
        title_lower = title.lower()
        content_lower = content.lower()

        for word in query_words:
            # Exact matches
            if word in title_lower:
                score += 3.0
            if word in content_lower:
                score += 1.5

            # Partial matches
            if any(word in larger_word for larger_word in title_lower.split()):
                score += 1.0
            if any(word in larger_word for larger_word in content_lower.split()):
                score += 0.5

        # Bonus for query phrase matches
        query_phrase = query.lower()
        if query_phrase in title_lower:
            score += 5.0
        if query_phrase in content_lower:
            score += 2.0

        return min(score / len(query_words) if query_words else 0.0, 10.0)  # Cap at 10

    def _analyze_content_patterns(self, content: str) -> Dict[str, Any]:
        """Analyze content for patterns and extract structured information."""
        analysis = {
            "has_code": bool(self.content_patterns["code"].search(content)),
            "links_count": len(self.content_patterns["links"].findall(content)),
            "emails_count": len(self.content_patterns["emails"].findall(content)),
            "dates_found": self.content_patterns["dates"].findall(content),
            "word_count": len(content.split()),
            "readability_score": self._calculate_readability_score(content)
        }

        return analysis

    def _classify_content_type(self, url: str, content: str) -> str:
        """Classify the type of content based on URL and content analysis."""
        url_lower = url.lower()
        content_lower = content.lower()

        # Documentation sites
        if any(domain in url_lower for domain in ['docs.', 'documentation', 'wiki', 'readme']):
            return "documentation"

        # Code repositories
        if any(domain in url_lower for domain in ['github.com', 'gitlab.com', 'bitbucket.org']):
            return "code_repository"

        # News sites
        if any(domain in url_lower for domain in ['news', 'cnn.com', 'bbc.com', 'reuters.com']):
            return "news"

        # Academic/research
        if any(domain in url_lower for domain in ['arxiv.org', 'scholar.google', 'researchgate']):
            return "academic"

        # Blogs
        if any(indicator in url_lower for indicator in ['blog', 'medium.com', 'dev.to']):
            return "blog"

        # Forums
        if any(domain in url_lower for domain in ['stackoverflow.com', 'reddit.com', 'forum']):
            return "forum"

        return "general"

    def _calculate_quality_score(self, result: Dict) -> float:
        """Calculate content quality score based on various factors."""
        score = 5.0  # Base score

        # URL quality indicators
        url = result.get("url", "").lower()
        if any(domain in url for domain in ['wikipedia.org', 'stackoverflow.com', 'github.com']):
            score += 2.0
        if url.startswith('https://'):
            score += 0.5

        # Content length (optimal range)
        content_length = len(result.get("content", result.get("snippet", "")))
        if 500 <= content_length <= 5000:
            score += 1.0
        elif content_length > 5000:
            score += 0.5

        # Title quality
        title = result.get("title", "")
        if title and len(title.split()) >= 3:
            score += 0.5

        return min(score, 10.0)  # Cap at 10

    def _calculate_readability_score(self, content: str) -> float:
        """Calculate a simple readability score."""
        if not content:
            return 0.0

        sentences = content.count('.') + content.count('!') + content.count('?')
        words = len(content.split())

        if sentences == 0:
            return 0.0

        avg_sentence_length = words / sentences

        # Simple readability score (lower is better)
        if avg_sentence_length <= 15:
            return 8.0  # Easy to read
        elif avg_sentence_length <= 25:
            return 6.0  # Moderate
        else:
            return 4.0  # Difficult

    async def _get_cached_search(self, query: str) -> Optional[ToolResult]:
        """Retrieve cached search results from database."""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT results, timestamp FROM search_cache WHERE query_hash = ? AND expiry > ?",
                    (query_hash, datetime.now())
                )
                row = cursor.fetchone()

                if row:
                    results = json.loads(row[0])
                    return ToolResult(
                        success=True,
                        data=results,
                        message="Search results retrieved from cache"
                    )
        except Exception as e:
            self.logger.warning(f"Failed to retrieve cached search: {e}")

        return None

    async def _cache_search_results(self, query: str, engine: str, results: List[Dict]):
        """Cache search results to database."""
        try:
            query_hash = hashlib.md5(query.encode()).hexdigest()
            expiry = datetime.now() + self.cache_ttl

            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    """INSERT OR REPLACE INTO search_cache
                       (query_hash, query, engine, results, timestamp, expiry)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (query_hash, query, engine, json.dumps(results), datetime.now(), expiry)
                )

                # Clean old cache entries
                conn.execute("DELETE FROM search_cache WHERE expiry < ?", (datetime.now(),))

        except Exception as e:
            self.logger.warning(f"Failed to cache search results: {e}")

    async def _get_cached_content(self, url: str) -> Optional[str]:
        """Retrieve cached content from database."""
        try:
            url_hash = hashlib.md5(url.encode()).hexdigest()

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT content FROM content_cache WHERE url_hash = ? AND expiry > ?",
                    (url_hash, datetime.now())
                )
                row = cursor.fetchone()

                if row:
                    return row[0]
        except Exception as e:
            self.logger.warning(f"Failed to retrieve cached content: {e}")

        return None

    async def _cache_content(self, url: str, content: str, metadata: Dict = None):
        """Cache content to database."""
        try:
            url_hash = hashlib.md5(url.encode()).hexdigest()
            expiry = datetime.now() + self.cache_ttl

            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    """INSERT OR REPLACE INTO content_cache
                       (url_hash, url, content, metadata, timestamp, expiry)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (url_hash, url, content, json.dumps(metadata or {}), datetime.now(), expiry)
                )

                # Clean old cache entries
                conn.execute("DELETE FROM content_cache WHERE expiry < ?", (datetime.now(),))

        except Exception as e:
            self.logger.warning(f"Failed to cache content: {e}")

    async def intelligent_extract(self, url: str, extract_type: str = "smart") -> ToolResult:
        """
        One-line intelligent content extraction with AI-powered analysis.

        Args:
            url: URL to extract content from
            extract_type: Type of extraction ("smart", "text", "structured", "metadata", "summary")
        """
        try:
            self.logger.info(f"Intelligent extraction from: {url}")

            # Check cache first
            cached_content = await self._get_cached_content(url)
            if cached_content and extract_type == "text":
                return ToolResult(
                    success=True,
                    data={"content": cached_content, "url": url, "cached": True},
                    message="Content extracted from cache"
                )

            # Extract content with enhanced processing
            content = await self._extract_content_enhanced(url)

            # Cache the raw content
            await self._cache_content(url, content)

            # Process based on extraction type
            if extract_type == "smart":
                # AI-powered smart extraction
                result_data = await self._smart_content_analysis(url, content)
            elif extract_type == "structured":
                result_data = await self._extract_structured_content(url)
            elif extract_type == "metadata":
                result_data = await self._extract_metadata(url)
            elif extract_type == "summary":
                result_data = await self._generate_content_summary(content, url)
            else:
                result_data = {"content": content, "url": url}

            return ToolResult(
                success=True,
                data=result_data,
                message=f"Intelligent {extract_type} extraction completed"
            )

        except Exception as e:
            self.logger.error(f"Intelligent extraction failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Intelligent extraction failed"
            )

    async def _extract_content_enhanced(self, url: str) -> str:
        """Enhanced content extraction with better parsing and error handling."""
        headers = {
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.request_timeout),
            headers=headers
        ) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # Remove unwanted elements
                    for element in soup(["script", "style", "nav", "header", "footer", "aside", "advertisement"]):
                        element.decompose()

                    # Try to find main content area
                    main_content = (
                        soup.find('main') or
                        soup.find('article') or
                        soup.find('div', class_=re.compile(r'content|main|article', re.I)) or
                        soup.find('div', id=re.compile(r'content|main|article', re.I)) or
                        soup.body
                    )

                    if main_content:
                        text = main_content.get_text()
                    else:
                        text = soup.get_text()

                    # Clean up text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    clean_text = ' '.join(chunk for chunk in chunks if chunk)

                    return clean_text[:self.max_content_length]
                else:
                    raise ToolError(f"Failed to fetch content: HTTP {response.status}")

    async def _smart_content_analysis(self, url: str, content: str) -> Dict[str, Any]:
        """Perform AI-powered smart analysis of content."""
        analysis = {
            "url": url,
            "content": content[:self.max_content_length],
            "content_type": self._classify_content_type(url, content),
            "patterns": self._analyze_content_patterns(content),
            "readability": self._calculate_readability_score(content),
            "key_topics": self._extract_key_topics(content),
            "summary": self._generate_quick_summary(content),
            "metadata": await self._extract_metadata(url)
        }

        return analysis

    def _extract_key_topics(self, content: str) -> List[str]:
        """Extract key topics from content using simple NLP techniques."""
        # Simple keyword extraction (can be enhanced with proper NLP libraries)
        words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]{4,}\b', content)
        word_freq = {}

        # Count word frequencies
        for word in words:
            word_lower = word.lower()
            if word_lower not in {'this', 'that', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'}:
                word_freq[word_lower] = word_freq.get(word_lower, 0) + 1

        # Return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:10] if freq > 1]

    def _generate_quick_summary(self, content: str) -> str:
        """Generate a quick summary of the content."""
        sentences = re.split(r'[.!?]+', content)

        # Filter out very short sentences
        meaningful_sentences = [s.strip() for s in sentences if len(s.strip()) > 20]

        if not meaningful_sentences:
            return "No meaningful content found."

        # Return first few sentences as summary
        summary_sentences = meaningful_sentences[:3]
        return '. '.join(summary_sentences) + '.'

    async def _generate_content_summary(self, content: str, url: str) -> Dict[str, Any]:
        """Generate a comprehensive summary of the content."""
        return {
            "url": url,
            "summary": self._generate_quick_summary(content),
            "key_topics": self._extract_key_topics(content),
            "content_analysis": self._analyze_content_patterns(content),
            "word_count": len(content.split()),
            "estimated_read_time": max(1, len(content.split()) // 200)  # Assuming 200 WPM
        }

    def _calculate_relevance(self, query: str, title: str, summary: str) -> float:
        """Calculate relevance score for search results (legacy method)."""
        return self._calculate_enhanced_relevance(query, title, summary)

    async def execute(self, **kwargs) -> ToolResult:
        """Execute web engine operations based on the action parameter.

        Args:
            **kwargs: Operation parameters including:
                - action: The operation to perform ('search', 'extract', 'monitor')
                - query: Search query (for search action)
                - url: URL to extract (for extract action)
                - max_results: Maximum number of results
                - Other action-specific parameters

        Returns:
            ToolResult with operation results
        """
        try:
            action = kwargs.get('action', 'search')

            if action == 'search':
                query = kwargs.get('query', '')
                max_results = kwargs.get('max_results', 5)
                include_analysis = kwargs.get('include_analysis', True)

                return await self.smart_search(
                    query=query,
                    max_results=max_results,
                    include_analysis=include_analysis
                )

            elif action == 'extract':
                url = kwargs.get('url', '')
                extract_type = kwargs.get('extract_type', 'smart')

                return await self.intelligent_extract(
                    url=url,
                    extract_type=extract_type
                )

            elif action == 'monitor':
                url = kwargs.get('url', '')
                interval = kwargs.get('interval', 3600)
                keywords = kwargs.get('keywords', [])

                # For now, return a placeholder result
                # In a full implementation, this would set up monitoring
                return ToolResult(
                    success=True,
                    data={
                        "message": f"Monitoring setup for {url}",
                        "interval": interval,
                        "keywords": keywords
                    }
                )

            else:
                return ToolResult(
                    success=False,
                    error=f"Unknown action: {action}"
                )

        except Exception as e:
            self.logger.error(f"Web engine execution failed: {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
