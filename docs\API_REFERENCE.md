# 🔌 Reverie CLI - API 参考文档

## 🎯 概述

Reverie CLI 提供了完整的REST API接口，支持所有核心功能的程序化访问。本文档详细介绍了所有可用的API端点、参数和响应格式。

## 🌐 基础信息

### 服务器地址
- **开发环境**: http://localhost:8000
- **生产环境**: 根据部署配置

### 认证方式
```http
# API密钥认证 (可选)
Authorization: Bearer YOUR_API_KEY

# 或使用Header
X-API-Key: YOUR_API_KEY
```

### 通用响应格式
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-12-19T10:30:00Z",
  "request_id": "req_123456789"
}
```

## 📚 核心API端点

### 🏥 健康检查 API

#### 基本健康检查
```http
GET /api/v1/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-19T10:30:00Z",
  "version": "2.0.0",
  "uptime": 3600
}
```

#### 详细健康检查
```http
GET /api/v1/health/detailed
```

**响应示例**:
```json
{
  "status": "healthy",
  "components": {
    "database": "healthy",
    "web_engine": "healthy",
    "context_engine": "healthy",
    "memory_engine": "healthy",
    "model_manager": "healthy"
  },
  "metrics": {
    "cpu_usage": 25.5,
    "memory_usage": 45.2,
    "disk_usage": 60.1,
    "active_connections": 5
  },
  "version": "2.0.0",
  "uptime": 3600
}
```

### 🤖 模型管理 API

#### 列出可用模型
```http
GET /api/v1/models
```

**响应示例**:
```json
{
  "models": [
    {
      "id": "lucy-128k",
      "name": "Lucy 128K",
      "type": "language_model",
      "context_length": 131072,
      "status": "available"
    },
    {
      "id": "gpt-4",
      "name": "GPT-4",
      "type": "language_model",
      "context_length": 8192,
      "status": "available"
    }
  ],
  "current_model": "lucy-128k"
}
```

#### 加载模型
```http
POST /api/v1/models/load
Content-Type: application/json

{
  "model_id": "lucy-128k",
  "config": {
    "temperature": 0.7,
    "max_tokens": 4096
  }
}
```

#### 获取当前模型信息
```http
GET /api/v1/models/current
```

**响应示例**:
```json
{
  "model": {
    "id": "lucy-128k",
    "name": "Lucy 128K",
    "type": "language_model",
    "context_length": 131072,
    "loaded_at": "2024-12-19T10:00:00Z",
    "config": {
      "temperature": 0.7,
      "max_tokens": 4096
    }
  }
}
```

### 💬 聊天和对话 API

#### 标准聊天完成
```http
POST /api/v1/chat/completions
Content-Type: application/json

{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful AI assistant."
    },
    {
      "role": "user",
      "content": "Explain FastAPI in simple terms."
    }
  ],
  "model": "lucy-128k",
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

**响应示例**:
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1703001000,
  "model": "lucy-128k",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "FastAPI is a modern, fast web framework for building APIs with Python..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 25,
    "completion_tokens": 150,
    "total_tokens": 175
  }
}
```

#### 增强聊天完成
```http
POST /api/v1/chat/enhanced
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "Help me implement JWT authentication in FastAPI"
    }
  ],
  "use_web_search": true,
  "use_memory": true,
  "use_context_analysis": true,
  "project_path": "./my-api-project",
  "context_type": "coding",
  "remember_conversation": true
}
```

**响应示例**:
```json
{
  "id": "chatcmpl-enhanced-123",
  "object": "chat.completion.enhanced",
  "created": 1703001000,
  "model": "lucy-128k",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Based on web research and your project context, here's how to implement JWT authentication in FastAPI..."
      },
      "finish_reason": "stop",
      "context_used": {
        "web_search": true,
        "project_analysis": true,
        "memory_context": true
      }
    }
  ],
  "usage": {
    "prompt_tokens": 150,
    "completion_tokens": 800,
    "total_tokens": 950
  },
  "engines_used": ["web", "context", "memory"]
}
```

#### 智能代码助手
```http
POST /api/v1/chat/smart-code
Content-Type: application/json

{
  "task": "Create a user authentication system",
  "code_context": "FastAPI application with SQLAlchemy",
  "project_path": "./api-project"
}
```

**响应示例**:
```json
{
  "response": "Here's a complete user authentication system for your FastAPI application...",
  "context_used": {
    "project_analysis": true,
    "web_research": true,
    "memory_context": true
  },
  "task": "Create a user authentication system",
  "suggestions": [
    "Add password hashing with bcrypt",
    "Implement refresh token mechanism",
    "Add rate limiting for login attempts"
  ]
}
```

### 🤖 增强代理 API

#### 执行代理任务
```http
POST /api/v1/agent/execute
Content-Type: application/json

{
  "task_type": "code",
  "description": "Create a REST API endpoint for user registration",
  "context": {
    "framework": "FastAPI",
    "database": "PostgreSQL"
  },
  "project_path": "./my-api",
  "use_engines": ["web", "context", "memory"],
  "priority": "normal",
  "remember_result": true
}
```

**响应示例**:
```json
{
  "task_id": "task-abc123",
  "status": "completed",
  "result": {
    "code": "from fastapi import FastAPI, HTTPException...",
    "explanation": "This endpoint handles user registration with validation...",
    "files_created": ["routes/auth.py", "models/user.py"],
    "dependencies": ["fastapi", "sqlalchemy", "bcrypt"]
  },
  "engines_used": ["web", "context", "memory"],
  "execution_time": 2.5,
  "suggestions": [
    "Add input validation with Pydantic",
    "Implement email verification",
    "Add rate limiting"
  ]
}
```

#### 智能助手
```http
POST /api/v1/agent/smart-assist
Content-Type: application/json

{
  "request": "How do I implement JWT authentication in FastAPI?",
  "context": {
    "project_path": "./my-api",
    "current_framework": "FastAPI"
  }
}
```

**响应示例**:
```json
{
  "response": "To implement JWT authentication in FastAPI, you'll need to...",
  "approach": "Smart coding assistance with AI engine integration",
  "engines_used": ["web", "context", "memory"],
  "confidence": 0.9,
  "follow_up_suggestions": [
    "Would you like me to generate the complete authentication code?",
    "Should I explain how to handle token refresh?",
    "Do you need help with user registration as well?"
  ]
}
```

#### 获取代理能力
```http
GET /api/v1/agent/capabilities
```

**响应示例**:
```json
{
  "task_types": [
    "code", "analyze", "search", "debug", "review", 
    "optimize", "security", "general"
  ],
  "available_engines": ["web", "context", "memory"],
  "features": {
    "web_search": true,
    "code_analysis": true,
    "memory_learning": true,
    "smart_assistance": true,
    "multi_engine_integration": true,
    "background_learning": true
  },
  "supported_languages": [
    "python", "javascript", "typescript", "java", "cpp", 
    "go", "rust", "ruby", "php", "csharp"
  ],
  "analysis_types": [
    "quick", "comprehensive", "security", "performance"
  ]
}
```

### 🛠️ 工具管理 API

#### 列出可用工具
```http
GET /api/v1/tools
```

**响应示例**:
```json
{
  "tools": [
    {
      "name": "web_search",
      "description": "Smart web search with AI analysis",
      "category": "web",
      "status": "available"
    },
    {
      "name": "code_analyze",
      "description": "Deep code analysis and understanding",
      "category": "context",
      "status": "available"
    },
    {
      "name": "memory_search",
      "description": "Intelligent memory search",
      "category": "memory",
      "status": "available"
    }
  ]
}
```

#### 执行工具
```http
POST /api/v1/tools/execute
Content-Type: application/json

{
  "tool_name": "web_search",
  "parameters": {
    "query": "FastAPI best practices 2024",
    "max_results": 5,
    "include_analysis": true
  }
}
```

**响应示例**:
```json
{
  "tool_name": "web_search",
  "status": "completed",
  "result": {
    "results": [
      {
        "title": "FastAPI Best Practices Guide 2024",
        "url": "https://example.com/fastapi-guide",
        "snippet": "Comprehensive guide to FastAPI best practices...",
        "relevance_score": 0.95
      }
    ],
    "analysis": {
      "total_results": 5,
      "avg_relevance": 0.87,
      "search_time": 1.2
    }
  },
  "execution_time": 1.5
}
```

### 📁 文件操作 API

#### 列出文件
```http
GET /api/v1/files?path=./src&recursive=true
```

**响应示例**:
```json
{
  "files": [
    {
      "name": "main.py",
      "path": "./src/main.py",
      "type": "file",
      "size": 1024,
      "modified": "2024-12-19T10:00:00Z"
    },
    {
      "name": "utils",
      "path": "./src/utils",
      "type": "directory",
      "children": 5
    }
  ],
  "total_files": 15,
  "total_size": 51200
}
```

#### 创建文件
```http
POST /api/v1/files/create
Content-Type: application/json

{
  "path": "./src/new_module.py",
  "content": "# New Python module\n\ndef hello_world():\n    return 'Hello, World!'",
  "overwrite": false
}
```

#### 读取文件
```http
GET /api/v1/files/read?path=./src/main.py
```

**响应示例**:
```json
{
  "path": "./src/main.py",
  "content": "from fastapi import FastAPI\n\napp = FastAPI()\n\<EMAIL>('/')\ndef read_root():\n    return {'Hello': 'World'}",
  "size": 98,
  "encoding": "utf-8",
  "modified": "2024-12-19T10:00:00Z"
}
```

### ⚙️ 配置管理 API

#### 获取配置
```http
GET /api/v1/config
```

**响应示例**:
```json
{
  "server": {
    "host": "127.0.0.1",
    "port": 8000,
    "workers": 1
  },
  "model": {
    "default_model": "lucy-128k",
    "temperature": 0.7,
    "max_tokens": 4096
  },
  "engines": {
    "web": {
      "enabled": true,
      "cache_ttl": 3600
    },
    "context": {
      "enabled": true,
      "analysis_depth": "comprehensive"
    },
    "memory": {
      "enabled": true,
      "max_items": 100000
    }
  }
}
```

#### 更新配置
```http
POST /api/v1/config/update
Content-Type: application/json

{
  "model.temperature": 0.8,
  "engines.web.cache_ttl": 7200,
  "engines.context.analysis_depth": "quick"
}
```

## 🔧 高级API功能

### 流式响应
对于支持流式响应的端点，设置 `stream: true`:

```http
POST /api/v1/chat/completions
Content-Type: application/json

{
  "messages": [...],
  "stream": true
}
```

**流式响应格式**:
```
data: {"choices":[{"delta":{"content":"Hello"}}]}

data: {"choices":[{"delta":{"content":" there"}}]}

data: [DONE]
```

### 批量操作
某些端点支持批量操作:

```http
POST /api/v1/tools/execute/batch
Content-Type: application/json

{
  "operations": [
    {
      "tool_name": "web_search",
      "parameters": {"query": "FastAPI tutorial"}
    },
    {
      "tool_name": "code_analyze",
      "parameters": {"path": "./src"}
    }
  ]
}
```

### 异步任务
长时间运行的任务返回任务ID:

```http
POST /api/v1/agent/execute
# 返回 task_id

GET /api/v1/agent/status/{task_id}
# 查询任务状态
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid parameter: model not found",
    "details": {
      "parameter": "model",
      "value": "invalid-model"
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 常见错误代码
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误
- `503` - 服务不可用

## 📊 速率限制

### 限制规则
- **标准端点**: 100 请求/分钟
- **AI生成端点**: 20 请求/分钟
- **文件操作**: 50 请求/分钟

### 限制头部
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1703001060
```

## 🔐 安全最佳实践

1. **使用HTTPS**: 生产环境必须使用HTTPS
2. **API密钥管理**: 定期轮换API密钥
3. **输入验证**: 验证所有输入参数
4. **访问控制**: 实施适当的访问控制
5. **日志监控**: 监控API访问日志

这个API参考文档提供了Reverie CLI所有REST API端点的详细信息，开发者可以根据需要集成到他们的应用程序中。
