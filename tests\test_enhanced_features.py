"""
Comprehensive tests for enhanced Reverie CLI features.

This module tests the enhanced AI engines, dual-mode console,
and API endpoints with integrated capabilities.
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.tools.memory_engine import MemoryEngine
from reverie_cli.core.console import DualModeConsole
from reverie_cli.agent.prompts import PromptManager


class TestWebEngine:
    """Test enhanced Web Engine functionality."""
    
    @pytest.fixture
    async def web_engine(self):
        """Create a Web Engine instance for testing."""
        return WebEngine()
    
    @pytest.mark.asyncio
    async def test_smart_search(self, web_engine):
        """Test smart search functionality."""
        # Mock the search to avoid actual web requests
        with patch.object(web_engine, '_search_with_fallback') as mock_search:
            mock_search.return_value = [
                {
                    "title": "Test Result",
                    "url": "https://example.com",
                    "snippet": "Test snippet",
                    "relevance_score": 0.9
                }
            ]
            
            result = await web_engine.smart_search(
                query="Python best practices",
                max_results=5,
                include_analysis=True
            )
            
            assert result.success
            assert "results" in result.data
            assert len(result.data["results"]) > 0
            mock_search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_intelligent_extract(self, web_engine):
        """Test intelligent content extraction."""
        with patch.object(web_engine, '_extract_content_enhanced') as mock_extract:
            mock_extract.return_value = "Test content"
            
            result = await web_engine.intelligent_extract(
                url="https://example.com",
                extract_type="smart"
            )
            
            assert result.success
            assert "content" in result.data
            mock_extract.assert_called_once()
    
    def test_content_analysis(self, web_engine):
        """Test content analysis patterns."""
        content = "def test_function(): pass"
        analysis = web_engine._analyze_content_patterns(content)
        
        assert "has_code" in analysis
        assert analysis["has_code"] is True
        assert "word_count" in analysis
        assert analysis["word_count"] > 0


class TestContextEngine:
    """Test enhanced Context Engine functionality."""
    
    @pytest.fixture
    async def context_engine(self):
        """Create a Context Engine instance for testing."""
        return ContextEngine()
    
    @pytest.fixture
    def temp_project(self):
        """Create a temporary project for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a simple Python project
            project_path = Path(temp_dir)
            
            # Create main.py
            (project_path / "main.py").write_text("""
def hello_world():
    '''A simple hello world function.'''
    print("Hello, World!")

class TestClass:
    '''A test class.'''
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value

if __name__ == "__main__":
    hello_world()
""")
            
            # Create requirements.txt
            (project_path / "requirements.txt").write_text("requests==2.28.0\nfastapi==0.95.0")
            
            yield str(project_path)
    
    @pytest.mark.asyncio
    async def test_smart_analyze(self, context_engine, temp_project):
        """Test smart project analysis."""
        result = await context_engine.smart_analyze(
            project_path=temp_project,
            analysis_type="quick",
            include_ai_insights=True
        )
        
        assert result.success
        assert "language" in result.data
        assert result.data["language"] == "python"
        assert "symbol_count" in result.data
        assert result.data["symbol_count"] > 0
    
    @pytest.mark.asyncio
    async def test_enhanced_python_parsing(self, context_engine, temp_project):
        """Test enhanced Python file parsing."""
        main_file = os.path.join(temp_project, "main.py")
        symbols, relationships = await context_engine._parse_python_file_enhanced(main_file)
        
        assert len(symbols) > 0
        
        # Check for function symbol
        function_symbols = [s for s in symbols.values() if s.type == "function"]
        assert len(function_symbols) > 0
        
        # Check for class symbol
        class_symbols = [s for s in symbols.values() if s.type == "class"]
        assert len(class_symbols) > 0
        
        # Verify symbol properties
        for symbol in symbols.values():
            assert hasattr(symbol, 'complexity_score')
            assert hasattr(symbol, 'quality_score')
            assert hasattr(symbol, 'semantic_tags')
    
    def test_complexity_calculation(self, context_engine):
        """Test cyclomatic complexity calculation."""
        import ast
        
        code = """
def complex_function(x):
    if x > 0:
        for i in range(x):
            if i % 2 == 0:
                print(i)
            else:
                continue
    else:
        return None
    return x
"""
        tree = ast.parse(code)
        func_node = tree.body[0]
        
        complexity = context_engine._calculate_cyclomatic_complexity(func_node)
        assert complexity > 1  # Should be more complex than base complexity


class TestMemoryEngine:
    """Test enhanced Memory Engine functionality."""
    
    @pytest.fixture
    async def memory_engine(self):
        """Create a Memory Engine instance for testing."""
        engine = MemoryEngine()
        # Use in-memory database for testing
        engine.db_path = ":memory:"
        engine._init_database()
        return engine
    
    @pytest.mark.asyncio
    async def test_smart_remember(self, memory_engine):
        """Test smart memory storage."""
        result = await memory_engine.smart_remember(
            content="This is a test memory item about Python programming",
            memory_type="test",
            importance=0.8,
            tags=["python", "test"]
        )
        
        assert result.success
        assert "memory_id" in result.data
        assert result.data["importance"] > 0
    
    @pytest.mark.asyncio
    async def test_intelligent_search(self, memory_engine):
        """Test intelligent memory search."""
        # First, store some test memories
        await memory_engine.smart_remember(
            content="Python is a programming language",
            memory_type="knowledge",
            importance=0.7
        )
        
        await memory_engine.smart_remember(
            content="FastAPI is a web framework for Python",
            memory_type="knowledge",
            importance=0.8
        )
        
        # Search for memories
        result = await memory_engine.intelligent_search(
            query="Python programming",
            search_type="smart",
            max_results=5
        )
        
        assert result.success
        assert "results" in result.data
        assert len(result.data["results"]) > 0
    
    def test_content_metadata_extraction(self, memory_engine):
        """Test content metadata extraction."""
        content = "def hello(): print('Hello, World!')"
        metadata = asyncio.run(memory_engine._extract_content_metadata(content))
        
        assert "word_count" in metadata
        assert "content_type" in metadata
        assert "language" in metadata
        assert metadata["content_type"] == "code"


class TestDualModeConsole:
    """Test enhanced dual-mode console functionality."""
    
    @pytest.fixture
    def console(self):
        """Create a dual-mode console for testing."""
        return DualModeConsole()
    
    def test_console_initialization(self, console):
        """Test console initialization."""
        assert console.ai_coder_mode is True
        assert console.api_service_mode is True
        assert console.current_context == "general"
        assert len(console.commands) > 0
    
    @pytest.mark.asyncio
    async def test_command_processing(self, console):
        """Test command processing."""
        # Mock the command execution
        with patch.object(console, 'show_help') as mock_help:
            await console._process_dual_mode_command("help")
            mock_help.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_natural_language_processing(self, console):
        """Test natural language request handling."""
        with patch.object(console, '_generate_ai_response') as mock_ai:
            mock_ai.return_value = "Test AI response"
            
            await console._handle_natural_language_request("Create a Python function")
            mock_ai.assert_called_once()
    
    def test_enhanced_completer(self, console):
        """Test AI-powered command completion."""
        from prompt_toolkit.document import Document
        
        completer = console.completer
        document = Document("cod")
        
        completions = list(completer.get_completions(document, None))
        completion_texts = [c.text for c in completions]
        
        assert "code" in completion_texts


class TestPromptManager:
    """Test enhanced prompt management."""
    
    @pytest.fixture
    def prompt_manager(self):
        """Create a prompt manager for testing."""
        return PromptManager()
    
    def test_base_system_prompt(self, prompt_manager):
        """Test base system prompt creation."""
        prompt = prompt_manager._create_base_system_prompt()
        
        assert "Reverie CLI" in prompt
        assert "AI-Native Development Assistant" in prompt
        assert "one-line" in prompt.lower()
    
    def test_dual_mode_prompt(self, prompt_manager):
        """Test dual-mode prompt creation."""
        prompt = prompt_manager.create_dual_mode_prompt(
            ai_coder_mode=True,
            api_service_mode=True,
            current_context="coding",
            available_engines=["web", "context", "memory"]
        )
        
        assert "Dual-Mode AI Assistant" in prompt
        assert "AI Coder Mode" in prompt
        assert "API Service Mode" in prompt
        assert "coding" in prompt
        assert "Web Engine" in prompt
    
    def test_system_prompt_creation(self, prompt_manager):
        """Test comprehensive system prompt creation."""
        context = {
            "project_path": "/test/project",
            "language": "python",
            "framework": "fastapi"
        }
        
        prompt = prompt_manager.create_system_prompt(
            context=context,
            available_tools=["web_search", "code_analyze"],
            user_preferences={"coding_style": "pep8"}
        )
        
        assert len(prompt) > 0
        assert "python" in prompt.lower()
        assert "fastapi" in prompt.lower()


class TestIntegration:
    """Test integration between enhanced components."""
    
    @pytest.mark.asyncio
    async def test_engine_integration(self):
        """Test integration between different engines."""
        # This would test how engines work together
        # For now, just verify they can be initialized together
        
        web_engine = WebEngine()
        context_engine = ContextEngine()
        memory_engine = MemoryEngine()
        
        assert web_engine is not None
        assert context_engine is not None
        assert memory_engine is not None
    
    @pytest.mark.asyncio
    async def test_api_integration(self):
        """Test API integration with enhanced features."""
        # This would test the enhanced API endpoints
        # For now, just verify the imports work
        
        from reverie_cli.api.routes.chat import router as chat_router
        from reverie_cli.api.routes.enhanced_agent import router as agent_router
        
        assert chat_router is not None
        assert agent_router is not None


# Test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Performance tests
class TestPerformance:
    """Test performance of enhanced features."""
    
    @pytest.mark.asyncio
    async def test_memory_performance(self):
        """Test memory engine performance."""
        memory_engine = MemoryEngine()
        
        # Test storing multiple items
        import time
        start_time = time.time()
        
        for i in range(10):
            await memory_engine.smart_remember(
                content=f"Test memory item {i}",
                memory_type="performance_test",
                importance=0.5
            )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within reasonable time
        assert execution_time < 5.0  # 5 seconds for 10 items
    
    def test_context_analysis_performance(self):
        """Test context analysis performance."""
        context_engine = ContextEngine()
        
        # Test with sample code
        sample_code = "def test(): pass\n" * 100  # 100 simple functions
        
        import time
        start_time = time.time()
        
        # This would test parsing performance
        # For now, just verify the engine initializes quickly
        assert context_engine is not None
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert execution_time < 1.0  # Should initialize quickly


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
