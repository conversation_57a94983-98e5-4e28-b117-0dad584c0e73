"""
Tool management and execution endpoints.
"""

from typing import List, Optional, Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.api.dependencies import get_tool_manager_dep


router = APIRouter()
logger = get_logger("tools")


class ToolInfo(BaseModel):
    """Tool information model."""
    name: str
    description: str
    parameters: Dict[str, Any]
    category: str
    status: str


class ToolExecuteRequest(BaseModel):
    """Tool execution request."""
    tool_name: str = Field(..., description="Name of the tool to execute")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters")


class ToolExecuteResponse(BaseModel):
    """Tool execution response."""
    success: bool
    result: Any
    message: str
    execution_time: float


@router.get("/tools", response_model=List[ToolInfo])
async def list_tools(tool_manager=Depends(get_tool_manager_dep)):
    """List all available tools."""
    try:
        tools_info = tool_manager.list_available_tools()

        # Convert to API model format
        tools = []
        for tool_info in tools_info:
            tools.append(ToolInfo(
                name=tool_info.name,
                description=tool_info.description,
                parameters={param.name: param.type for param in tool_info.parameters},
                category=tool_info.category.value,
                status="ready" if tool_manager.is_tool_enabled(tool_info.name) else "disabled"
            ))

        return tools

    except Exception as e:
        logger.error(f"Failed to list tools: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list tools: {e}")


@router.post("/tools/execute", response_model=ToolExecuteResponse)
async def execute_tool(request: ToolExecuteRequest, tool_manager=Depends(get_tool_manager_dep)):
    """Execute a tool with given parameters."""
    try:
        logger.info(f"Executing tool: {request.tool_name}")

        # Execute the tool
        result = await tool_manager.execute_tool(
            request.tool_name,
            request.parameters
        )

        return ToolExecuteResponse(
            success=result.success,
            result=result.result,
            message=result.message,
            execution_time=result.execution_time
        )

    except Exception as e:
        logger.error(f"Tool execution failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {e}")


@router.get("/tools/{tool_name}")
async def get_tool_info(tool_name: str):
    """Get information about a specific tool."""
    # TODO: Get actual tool info
    return {"name": tool_name, "status": "ready"}
