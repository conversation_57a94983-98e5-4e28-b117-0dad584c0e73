"""
API routes for Reverie CLI.

This module contains all the API route definitions organized by functionality:
- health: Health checks and system status
- models: Model management and information
- chat: Chat and completion endpoints
- enhanced_agent: Enhanced AI agent interaction with engines
- config: Configuration management
- tools: Tool management and execution
- files: File operations and management
"""

# Import all route modules to make them available
from . import health, models, chat, enhanced_agent, tools, files, config

__all__ = [
    "health",
    "models",
    "chat",
    "enhanced_agent",
    "tools",
    "files",
    "config",
]
