"""
Task execution engine for AI agents.
"""

import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import Agent<PERSON><PERSON>r, AgentTimeoutError, AgentMaxIterationsError
from reverie_cli.agent.planner import Task, TaskStatus, TaskType
from reverie_cli.agent.memory import MemoryManager, MemoryType
from reverie_cli.models.manager import get_model_manager
from reverie_cli.tools.manager import get_tool_manager


class TaskExecutor:
    """
    Task execution engine for AI agents.
    
    Executes planned tasks using available tools and AI models,
    with support for error handling, retries, and progress tracking.
    """
    
    def __init__(self, memory_manager: MemoryManager):
        self.logger = get_logger("task_executor")
        self.memory_manager = memory_manager
        
        # Execution state
        self.current_task: Optional[Task] = None
        self.execution_history: List[Dict[str, Any]] = []
        
        self.logger.info("TaskExecutor initialized")
    
    async def execute_tasks(
        self,
        tasks: List[Task],
        max_iterations: int = 50,
        timeout_seconds: int = 300
    ) -> List[Dict[str, Any]]:
        """
        Execute a list of tasks.
        
        Args:
            tasks: List of tasks to execute
            max_iterations: Maximum iterations per task
            timeout_seconds: Total execution timeout
            
        Returns:
            List of execution results
        """
        self.logger.info(f"Executing {len(tasks)} tasks")
        
        start_time = datetime.now()
        results = []
        
        try:
            for i, task in enumerate(tasks):
                # Check timeout
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > timeout_seconds:
                    raise AgentTimeoutError(f"Task execution timed out after {elapsed:.1f}s")
                
                self.logger.info(f"Executing task {i+1}/{len(tasks)}: {task.description}")
                
                # Execute single task
                result = await self.execute_task(task, max_iterations)
                results.append(result)
                
                # Store in memory
                self.memory_manager.add_memory(
                    content=f"Task executed: {task.description}",
                    memory_type=MemoryType.TASK,
                    metadata={
                        "task_id": task.id,
                        "task_type": task.task_type.value,
                        "status": task.status.value,
                        "result": result
                    },
                    importance=0.7
                )
                
                # Stop if task failed and is critical
                if task.status == TaskStatus.FAILED and task.estimated_complexity > 0.8:
                    self.logger.warning(f"Critical task failed, stopping execution: {task.id}")
                    break
            
            total_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(f"Completed task execution in {total_time:.1f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            raise AgentError(f"Task execution failed: {e}")
    
    async def execute_task(self, task: Task, max_iterations: int = 10) -> Dict[str, Any]:
        """
        Execute a single task.
        
        Args:
            task: Task to execute
            max_iterations: Maximum iterations for this task
            
        Returns:
            Execution result
        """
        self.current_task = task
        task.status = TaskStatus.IN_PROGRESS
        
        start_time = datetime.now()
        iterations = 0
        
        try:
            while iterations < max_iterations:
                iterations += 1
                
                self.logger.debug(f"Task {task.id} iteration {iterations}")
                
                # Execute based on task type
                if task.task_type == TaskType.CODE_GENERATION:
                    result = await self._execute_code_generation(task)
                elif task.task_type == TaskType.FILE_OPERATION:
                    result = await self._execute_file_operation(task)
                elif task.task_type == TaskType.ANALYSIS:
                    result = await self._execute_analysis(task)
                elif task.task_type == TaskType.EXECUTION:
                    result = await self._execute_code_execution(task)
                elif task.task_type == TaskType.COMMUNICATION:
                    result = await self._execute_communication(task)
                else:
                    result = await self._execute_generic(task)
                
                # Check if task completed successfully
                if result.get("success", False):
                    task.status = TaskStatus.COMPLETED
                    task.result = result
                    break
                else:
                    # Task failed, but might retry
                    if iterations >= max_iterations:
                        task.status = TaskStatus.FAILED
                        task.error = result.get("error", "Unknown error")
                        break
            
            if iterations >= max_iterations and task.status != TaskStatus.COMPLETED:
                raise AgentMaxIterationsError(f"Task exceeded maximum iterations: {task.id}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            execution_result = {
                "task_id": task.id,
                "status": task.status.value,
                "result": task.result,
                "error": task.error,
                "iterations": iterations,
                "execution_time": execution_time
            }
            
            self.execution_history.append(execution_result)
            return execution_result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            execution_result = {
                "task_id": task.id,
                "status": task.status.value,
                "result": None,
                "error": str(e),
                "iterations": iterations,
                "execution_time": execution_time
            }
            
            self.execution_history.append(execution_result)
            self.logger.error(f"Task execution failed: {task.id} - {e}")
            return execution_result
        
        finally:
            self.current_task = None
    
    async def _execute_code_generation(self, task: Task) -> Dict[str, Any]:
        """Execute code generation task."""
        try:
            model_manager = get_model_manager()
            
            # Create code generation prompt
            prompt = f"""
Generate code for the following task: {task.description}

Please provide clean, well-commented code that accomplishes the task.
If the task involves creating a file, include the filename and full code content.

Task: {task.description}
"""
            
            # Get AI response
            response = await model_manager.generate(
                prompt=prompt,
                max_tokens=2000,
                temperature=0.3
            )
            
            # If task requires writing to file, do it
            if 'write_file' in task.tools_required:
                await self._handle_file_writing(response, task)
            
            return {
                "success": True,
                "result": response,
                "type": "code_generation"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "code_generation"
            }
    
    async def _execute_file_operation(self, task: Task) -> Dict[str, Any]:
        """Execute file operation task."""
        try:
            tool_manager = get_tool_manager()
            
            # Determine the specific file operation
            description_lower = task.description.lower()
            
            if 'read' in description_lower:
                # Extract filename from description
                filename = self._extract_filename(task.description)
                if filename:
                    result = await tool_manager.execute_tool(
                        "read_file",
                        {"path": filename}
                    )
                    return {
                        "success": result.success,
                        "result": result.result,
                        "error": result.error,
                        "type": "file_read"
                    }
            
            elif 'write' in description_lower or 'create' in description_lower:
                # This should be handled by code generation + file writing
                return {
                    "success": True,
                    "result": "File operation delegated to code generation",
                    "type": "file_write_delegated"
                }
            
            elif 'list' in description_lower:
                # List directory
                directory = self._extract_directory(task.description) or "."
                result = await tool_manager.execute_tool(
                    "list_directory",
                    {"path": directory}
                )
                return {
                    "success": result.success,
                    "result": result.result,
                    "error": result.error,
                    "type": "directory_list"
                }
            
            elif 'delete' in description_lower:
                filename = self._extract_filename(task.description)
                if filename:
                    result = await tool_manager.execute_tool(
                        "delete_file",
                        {"path": filename}
                    )
                    return {
                        "success": result.success,
                        "result": result.result,
                        "error": result.error,
                        "type": "file_delete"
                    }
            
            return {
                "success": False,
                "error": "Could not determine specific file operation",
                "type": "file_operation"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "file_operation"
            }

    async def _execute_analysis(self, task: Task) -> Dict[str, Any]:
        """Execute code analysis task."""
        try:
            model_manager = get_model_manager()

            # Get context for analysis
            context = ""
            if 'read_file' in task.tools_required:
                filename = self._extract_filename(task.description)
                if filename:
                    tool_manager = get_tool_manager()
                    file_result = await tool_manager.execute_tool(
                        "read_file",
                        {"path": filename}
                    )
                    if file_result.success:
                        context = f"File content:\n{file_result.result}\n\n"

            # Create analysis prompt
            prompt = f"""
Analyze the following request: {task.description}

{context}

Please provide a detailed analysis including:
- Code structure and organization
- Potential issues or improvements
- Best practices recommendations
- Any bugs or errors found

Analysis:
"""

            # Get AI response
            response = await model_manager.generate(
                prompt=prompt,
                max_tokens=1500,
                temperature=0.3
            )

            return {
                "success": True,
                "result": response,
                "type": "analysis"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "analysis"
            }

    async def _execute_code_execution(self, task: Task) -> Dict[str, Any]:
        """Execute code execution task."""
        try:
            tool_manager = get_tool_manager()

            # Determine what to execute
            description_lower = task.description.lower()

            if 'python' in description_lower:
                # Extract or read Python code
                code = self._extract_code(task.description)
                if not code:
                    # Try to read from file
                    filename = self._extract_filename(task.description)
                    if filename:
                        file_result = await tool_manager.execute_tool(
                            "read_file",
                            {"path": filename}
                        )
                        if file_result.success:
                            code = file_result.result

                if code:
                    result = await tool_manager.execute_tool(
                        "execute_python",
                        {"code": code}
                    )
                    return {
                        "success": result.success,
                        "result": result.result,
                        "error": result.error,
                        "type": "python_execution"
                    }

            elif any(word in description_lower for word in ['shell', 'command', 'bash']):
                command = self._extract_command(task.description)
                if command:
                    result = await tool_manager.execute_tool(
                        "execute_shell",
                        {"command": command}
                    )
                    return {
                        "success": result.success,
                        "result": result.result,
                        "error": result.error,
                        "type": "shell_execution"
                    }

            return {
                "success": False,
                "error": "Could not determine what to execute",
                "type": "execution"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "execution"
            }

    async def _execute_communication(self, task: Task) -> Dict[str, Any]:
        """Execute communication task."""
        try:
            model_manager = get_model_manager()

            # Create communication prompt
            prompt = f"""
Respond to the following request: {task.description}

Please provide a helpful, informative response that addresses the user's needs.

Response:
"""

            # Get AI response
            response = await model_manager.generate(
                prompt=prompt,
                max_tokens=1000,
                temperature=0.7
            )

            return {
                "success": True,
                "result": response,
                "type": "communication"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "communication"
            }

    async def _execute_generic(self, task: Task) -> Dict[str, Any]:
        """Execute generic task."""
        try:
            model_manager = get_model_manager()

            # Create generic prompt
            prompt = f"""
Complete the following task: {task.description}

Please provide a detailed response that accomplishes the requested task.

Response:
"""

            # Get AI response
            response = await model_manager.generate(
                prompt=prompt,
                max_tokens=1500,
                temperature=0.5
            )

            return {
                "success": True,
                "result": response,
                "type": "generic"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": "generic"
            }

    # Helper methods
    def _extract_filename(self, text: str) -> Optional[str]:
        """Extract filename from text."""
        # Look for common file patterns
        patterns = [
            r"file\s+['\"]([^'\"]+)['\"]",
            r"['\"]([^'\"]*\.[a-zA-Z0-9]+)['\"]",
            r"(\w+\.[a-zA-Z0-9]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return None

    def _extract_directory(self, text: str) -> Optional[str]:
        """Extract directory from text."""
        patterns = [
            r"directory\s+['\"]([^'\"]+)['\"]",
            r"folder\s+['\"]([^'\"]+)['\"]",
            r"in\s+['\"]([^'\"]+)['\"]",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return None

    def _extract_code(self, text: str) -> Optional[str]:
        """Extract code from text."""
        # Look for code blocks
        patterns = [
            r"```python\n(.*?)```",
            r"```\n(.*?)```",
            r"code:\s*(.*?)(?:\n|$)",
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()

        return None

    def _extract_command(self, text: str) -> Optional[str]:
        """Extract shell command from text."""
        patterns = [
            r"command\s+['\"]([^'\"]+)['\"]",
            r"run\s+['\"]([^'\"]+)['\"]",
            r"execute\s+['\"]([^'\"]+)['\"]",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return None

    async def _handle_file_writing(self, content: str, task: Task):
        """Handle writing content to file."""
        try:
            tool_manager = get_tool_manager()

            # Extract filename and code from content
            filename = self._extract_filename(task.description) or self._extract_filename(content)

            if not filename:
                # Generate filename based on task
                if 'python' in task.description.lower():
                    filename = "generated_code.py"
                else:
                    filename = "generated_file.txt"

            # Extract code content
            code_content = self._extract_code(content) or content

            # Write file
            result = await tool_manager.execute_tool(
                "write_file",
                {
                    "path": filename,
                    "content": code_content
                }
            )

            if result.success:
                self.logger.info(f"Successfully wrote file: {filename}")
            else:
                self.logger.error(f"Failed to write file: {result.error}")

        except Exception as e:
            self.logger.error(f"File writing failed: {e}")

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        if not self.execution_history:
            return {"total_executions": 0}

        total_executions = len(self.execution_history)
        successful = sum(1 for r in self.execution_history if r["status"] == "completed")
        failed = sum(1 for r in self.execution_history if r["status"] == "failed")

        total_time = sum(r["execution_time"] for r in self.execution_history)
        avg_time = total_time / total_executions if total_executions > 0 else 0

        return {
            "total_executions": total_executions,
            "successful": successful,
            "failed": failed,
            "success_rate": successful / total_executions if total_executions > 0 else 0,
            "total_execution_time": total_time,
            "average_execution_time": avg_time
        }
