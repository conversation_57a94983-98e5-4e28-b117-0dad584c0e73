"""
Advanced learning and adaptation system for Reverie CLI.

This module provides intelligent learning capabilities that enable the AI
to adapt to user preferences, coding patterns, and project contexts over time.
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import pickle

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings


@dataclass
class LearningPattern:
    """Represents a learned pattern."""
    pattern_id: str
    pattern_type: str  # user_behavior, code_style, project_pattern, etc.
    pattern_data: Dict[str, Any]
    confidence: float
    frequency: int
    last_seen: datetime
    created_at: datetime


@dataclass
class UserProfile:
    """Represents a user's learned profile."""
    user_id: str
    preferences: Dict[str, Any]
    coding_style: Dict[str, Any]
    common_patterns: List[str]
    skill_level: str  # beginner, intermediate, advanced
    preferred_languages: List[str]
    work_patterns: Dict[str, Any]
    last_updated: datetime


class LearningEngine:
    """
    Advanced learning engine that adapts to user behavior and preferences.
    
    Provides intelligent adaptation based on:
    - User interaction patterns
    - Code style preferences
    - Project structure patterns
    - Tool usage patterns
    - Success/failure feedback
    """
    
    def __init__(self):
        self.logger = get_logger("learning_engine")
        self.settings = get_settings()
        
        # Learning data storage
        self.patterns: Dict[str, LearningPattern] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
        self.interaction_history: List[Dict[str, Any]] = []
        
        # Learning parameters
        self.min_confidence_threshold = 0.6
        self.pattern_decay_rate = 0.95  # Daily decay for unused patterns
        self.learning_rate = 0.1
        
        # Pattern categories
        self.pattern_categories = {
            "user_behavior": ["request_style", "response_preference", "tool_usage"],
            "code_style": ["naming_convention", "structure_preference", "comment_style"],
            "project_pattern": ["architecture_style", "file_organization", "dependency_pattern"],
            "workflow": ["development_process", "testing_approach", "deployment_style"]
        }
        
        # Load existing learning data
        self._load_learning_data()
        
        self.logger.info("LearningEngine initialized")
    
    def learn_from_interaction(
        self,
        user_id: str,
        interaction_data: Dict[str, Any],
        success_score: float = 1.0
    ):
        """
        Learn from a user interaction.
        
        Args:
            user_id: User identifier
            interaction_data: Interaction details
            success_score: Success score (0.0 to 1.0)
        """
        try:
            # Record interaction
            interaction_record = {
                "user_id": user_id,
                "timestamp": datetime.now(),
                "data": interaction_data,
                "success_score": success_score
            }
            self.interaction_history.append(interaction_record)
            
            # Extract patterns from interaction
            patterns = self._extract_patterns(interaction_data)
            
            # Update or create patterns
            for pattern_type, pattern_data in patterns.items():
                self._update_pattern(user_id, pattern_type, pattern_data, success_score)
            
            # Update user profile
            self._update_user_profile(user_id, interaction_data, success_score)
            
            # Cleanup old data
            self._cleanup_old_data()
            
        except Exception as e:
            self.logger.error(f"Failed to learn from interaction: {e}")
    
    def get_user_recommendations(self, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get personalized recommendations for a user.
        
        Args:
            user_id: User identifier
            context: Current context
            
        Returns:
            Personalized recommendations
        """
        try:
            profile = self.user_profiles.get(user_id)
            if not profile:
                return self._get_default_recommendations()
            
            recommendations = {
                "response_style": self._recommend_response_style(profile, context),
                "tools": self._recommend_tools(profile, context),
                "code_suggestions": self._recommend_code_style(profile, context),
                "workflow": self._recommend_workflow(profile, context)
            }
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Failed to get recommendations: {e}")
            return self._get_default_recommendations()
    
    def analyze_code_patterns(self, code: str, language: str) -> Dict[str, Any]:
        """
        Analyze code patterns for learning.
        
        Args:
            code: Code content
            language: Programming language
            
        Returns:
            Extracted code patterns
        """
        patterns = {
            "language": language,
            "length": len(code.split('\n')),
            "complexity": self._estimate_complexity(code),
            "style": self._analyze_code_style(code, language),
            "patterns": self._detect_code_patterns(code, language)
        }
        
        return patterns
    
    def predict_user_intent(self, user_input: str, user_id: str) -> Dict[str, Any]:
        """
        Predict user intent based on learned patterns.
        
        Args:
            user_input: User's input
            user_id: User identifier
            
        Returns:
            Intent prediction with confidence
        """
        try:
            profile = self.user_profiles.get(user_id)
            
            # Analyze input patterns
            input_features = self._extract_input_features(user_input)
            
            # Match against learned patterns
            intent_scores = {}
            
            # Common intent categories
            intents = [
                "code_generation", "code_review", "debugging", "explanation",
                "refactoring", "testing", "documentation", "research"
            ]
            
            for intent in intents:
                score = self._calculate_intent_score(input_features, intent, profile)
                intent_scores[intent] = score
            
            # Get top intent
            top_intent = max(intent_scores, key=intent_scores.get)
            confidence = intent_scores[top_intent]
            
            return {
                "intent": top_intent,
                "confidence": confidence,
                "all_scores": intent_scores,
                "features": input_features
            }
            
        except Exception as e:
            self.logger.error(f"Failed to predict intent: {e}")
            return {"intent": "general", "confidence": 0.5}
    
    def _extract_patterns(self, interaction_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract patterns from interaction data."""
        patterns = {}
        
        # User behavior patterns
        if "user_input" in interaction_data:
            user_input = interaction_data["user_input"]
            patterns["request_style"] = {
                "length": len(user_input.split()),
                "formality": self._assess_formality(user_input),
                "specificity": self._assess_specificity(user_input),
                "includes_context": "context" in user_input.lower()
            }
        
        # Response preference patterns
        if "ai_response" in interaction_data and "user_feedback" in interaction_data:
            response = interaction_data["ai_response"]
            feedback = interaction_data["user_feedback"]
            patterns["response_preference"] = {
                "preferred_length": len(response.split()),
                "likes_examples": "example" in feedback.lower(),
                "likes_explanations": "explain" in feedback.lower(),
                "prefers_code": "code" in feedback.lower()
            }
        
        # Tool usage patterns
        if "tools_used" in interaction_data:
            tools = interaction_data["tools_used"]
            patterns["tool_usage"] = {
                "tools": tools,
                "frequency": len(tools),
                "categories": [self._categorize_tool(tool) for tool in tools]
            }
        
        return patterns
    
    def _update_pattern(
        self,
        user_id: str,
        pattern_type: str,
        pattern_data: Dict[str, Any],
        success_score: float
    ):
        """Update or create a learning pattern."""
        pattern_id = f"{user_id}:{pattern_type}"
        
        if pattern_id in self.patterns:
            # Update existing pattern
            pattern = self.patterns[pattern_id]
            pattern.frequency += 1
            pattern.last_seen = datetime.now()
            
            # Update confidence based on success
            if success_score > 0.7:
                pattern.confidence = min(1.0, pattern.confidence + self.learning_rate)
            elif success_score < 0.3:
                pattern.confidence = max(0.0, pattern.confidence - self.learning_rate)
            
            # Merge pattern data
            for key, value in pattern_data.items():
                if key in pattern.pattern_data:
                    if isinstance(value, (int, float)):
                        # Average numerical values
                        pattern.pattern_data[key] = (pattern.pattern_data[key] + value) / 2
                    elif isinstance(value, list):
                        # Merge lists
                        pattern.pattern_data[key].extend(value)
                else:
                    pattern.pattern_data[key] = value
        else:
            # Create new pattern
            self.patterns[pattern_id] = LearningPattern(
                pattern_id=pattern_id,
                pattern_type=pattern_type,
                pattern_data=pattern_data,
                confidence=success_score,
                frequency=1,
                last_seen=datetime.now(),
                created_at=datetime.now()
            )
    
    def _update_user_profile(
        self,
        user_id: str,
        interaction_data: Dict[str, Any],
        success_score: float
    ):
        """Update user profile based on interaction."""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                preferences={},
                coding_style={},
                common_patterns=[],
                skill_level="intermediate",
                preferred_languages=[],
                work_patterns={},
                last_updated=datetime.now()
            )
        
        profile = self.user_profiles[user_id]
        
        # Update preferences based on successful interactions
        if success_score > 0.7:
            if "response_length" in interaction_data:
                profile.preferences["response_length"] = interaction_data["response_length"]
            
            if "language" in interaction_data:
                lang = interaction_data["language"]
                if lang not in profile.preferred_languages:
                    profile.preferred_languages.append(lang)
        
        profile.last_updated = datetime.now()
    
    def _get_default_recommendations(self) -> Dict[str, Any]:
        """Get default recommendations for new users."""
        return {
            "response_style": "balanced",
            "tools": ["web_search", "context_analysis"],
            "code_suggestions": "standard",
            "workflow": "iterative"
        }
    
    def _recommend_response_style(self, profile: UserProfile, context: Dict[str, Any]) -> str:
        """Recommend response style based on user profile."""
        if profile.preferences.get("detailed_explanations", 0) > 5:
            return "detailed"
        elif profile.preferences.get("concise_responses", 0) > 5:
            return "concise"
        else:
            return "balanced"
    
    def _recommend_tools(self, profile: UserProfile, context: Dict[str, Any]) -> List[str]:
        """Recommend tools based on user profile and context."""
        recommended = []
        
        # Based on common patterns
        if "web_search" in profile.common_patterns:
            recommended.append("web_engine")
        
        if "code_analysis" in profile.common_patterns:
            recommended.append("context_engine")
        
        # Based on context
        if context.get("project_path"):
            recommended.append("context_engine")
        
        if context.get("needs_research"):
            recommended.append("web_engine")
        
        return recommended or ["web_engine", "context_engine"]
    
    def _load_learning_data(self):
        """Load existing learning data from storage."""
        try:
            data_file = os.path.join(self.settings.data_dir, "learning_data.pkl")
            if os.path.exists(data_file):
                with open(data_file, 'rb') as f:
                    data = pickle.load(f)
                    self.patterns = data.get("patterns", {})
                    self.user_profiles = data.get("user_profiles", {})
                    self.interaction_history = data.get("interaction_history", [])
                
                self.logger.info(f"Loaded {len(self.patterns)} patterns and {len(self.user_profiles)} profiles")
        except Exception as e:
            self.logger.warning(f"Failed to load learning data: {e}")
    
    def save_learning_data(self):
        """Save learning data to storage."""
        try:
            os.makedirs(self.settings.data_dir, exist_ok=True)
            data_file = os.path.join(self.settings.data_dir, "learning_data.pkl")
            
            data = {
                "patterns": self.patterns,
                "user_profiles": self.user_profiles,
                "interaction_history": self.interaction_history[-1000:]  # Keep last 1000 interactions
            }
            
            with open(data_file, 'wb') as f:
                pickle.dump(data, f)
            
            self.logger.info("Learning data saved successfully")
        except Exception as e:
            self.logger.error(f"Failed to save learning data: {e}")
    
    def _cleanup_old_data(self):
        """Clean up old learning data."""
        cutoff_date = datetime.now() - timedelta(days=30)
        
        # Remove old patterns with low confidence
        patterns_to_remove = []
        for pattern_id, pattern in self.patterns.items():
            if pattern.last_seen < cutoff_date and pattern.confidence < self.min_confidence_threshold:
                patterns_to_remove.append(pattern_id)
        
        for pattern_id in patterns_to_remove:
            del self.patterns[pattern_id]
        
        # Limit interaction history
        if len(self.interaction_history) > 10000:
            self.interaction_history = self.interaction_history[-5000:]
