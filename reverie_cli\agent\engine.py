"""
Main AI Agent Engine for Reverie CLI.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings
from reverie_cli.core.exceptions import Agent<PERSON>rror
from reverie_cli.agent.memory import Memory<PERSON>anager, MemoryType
from reverie_cli.agent.planner import TaskPlanner, Task
from reverie_cli.agent.executor import TaskExecutor
from reverie_cli.models.manager import get_model_manager
from reverie_cli.tools.manager import get_tool_manager
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.agent.prompts import PromptManager


class AgentEngine:
    """
    Main AI Agent Engine.
    
    Coordinates task planning, execution, and memory management
    to provide intelligent AI assistance for coding tasks.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("agent_engine")
        
        # Core components
        self.memory_manager = MemoryManager(
            max_items=self.settings.agent.max_memory_items
        )
        self.task_planner = TaskPlanner()
        self.task_executor = TaskExecutor(self.memory_manager)

        # Enhanced engines
        self.web_engine = WebEngine()
        self.context_engine = ContextEngine()
        self.prompt_manager = PromptManager()
        
        # Agent state
        self.session_id: Optional[str] = None
        self.active_tasks: List[Task] = []
        self.completed_tasks: List[Task] = []
        
        # Performance tracking
        self.total_requests = 0
        self.successful_requests = 0
        self.start_time = datetime.now()
        
        self.logger.info("AgentEngine initialized")
    
    async def initialize(self):
        """Initialize the agent engine."""
        self.logger.info("Initializing AgentEngine")
        
        # Generate session ID
        import uuid
        self.session_id = str(uuid.uuid4())
        
        # Add initial system memory
        self.memory_manager.add_memory(
            content="Agent session started",
            memory_type=MemoryType.CONVERSATION,
            metadata={
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            },
            importance=0.3
        )
        
        self.logger.info(f"AgentEngine initialized with session: {self.session_id}")
    
    async def process_request(
        self,
        user_request: str,
        context: Optional[Dict[str, Any]] = None,
        max_iterations: Optional[int] = None,
        timeout_seconds: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process a user request using the agent system.
        
        Args:
            user_request: User's request description
            context: Additional context information
            max_iterations: Maximum iterations (uses config default if None)
            timeout_seconds: Timeout in seconds (uses config default if None)
            
        Returns:
            Processing result with tasks and outputs
        """
        self.total_requests += 1
        request_start = datetime.now()
        
        # Use config defaults if not provided
        if max_iterations is None:
            max_iterations = self.settings.agent.max_iterations
        if timeout_seconds is None:
            timeout_seconds = self.settings.agent.timeout_seconds
        
        self.logger.info(f"Processing request: {user_request}")
        
        try:
            # Add user request to memory
            self.memory_manager.add_memory(
                content=user_request,
                memory_type=MemoryType.CONVERSATION,
                metadata={
                    "role": "user",
                    "session_id": self.session_id,
                    "request_id": self.total_requests
                },
                importance=0.8
            )
            
            # Get relevant context from memory
            memory_context = self._get_relevant_context(user_request)
            if context:
                context.update(memory_context)
            else:
                context = memory_context
            
            # Plan tasks
            self.logger.info("Planning tasks...")
            tasks = await self.task_planner.plan_tasks(user_request, context)
            self.active_tasks = tasks
            
            # Execute tasks
            self.logger.info(f"Executing {len(tasks)} tasks...")
            execution_results = await self.task_executor.execute_tasks(
                tasks,
                max_iterations=max_iterations,
                timeout_seconds=timeout_seconds
            )
            
            # Process results
            final_result = self._process_execution_results(tasks, execution_results)
            
            # Add result to memory
            self.memory_manager.add_memory(
                content=final_result.get("summary", "Task completed"),
                memory_type=MemoryType.CONVERSATION,
                metadata={
                    "role": "assistant",
                    "session_id": self.session_id,
                    "request_id": self.total_requests,
                    "tasks_count": len(tasks),
                    "success": final_result.get("success", False)
                },
                importance=0.8
            )
            
            # Update completed tasks
            self.completed_tasks.extend([t for t in tasks if t.status.value in ["completed", "failed"]])
            self.active_tasks = []
            
            # Update success counter
            if final_result.get("success", False):
                self.successful_requests += 1
            
            processing_time = (datetime.now() - request_start).total_seconds()
            final_result["processing_time"] = processing_time
            
            self.logger.info(f"Request processed in {processing_time:.2f}s")
            return final_result
            
        except Exception as e:
            self.logger.error(f"Request processing failed: {e}")
            
            # Add error to memory
            self.memory_manager.add_memory(
                content=f"Error processing request: {str(e)}",
                memory_type=MemoryType.ERROR,
                metadata={
                    "session_id": self.session_id,
                    "request_id": self.total_requests,
                    "error_type": type(e).__name__
                },
                importance=0.9
            )
            
            processing_time = (datetime.now() - request_start).total_seconds()
            
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "processing_time": processing_time,
                "tasks": [],
                "results": []
            }
    
    def _get_relevant_context(self, user_request: str) -> Dict[str, Any]:
        """Get relevant context from memory."""
        # Search for relevant memories
        relevant_memories = self.memory_manager.search_memories(
            query=user_request,
            limit=5
        )
        
        # Get recent conversation context
        recent_context = self.memory_manager.get_conversation_context(limit=10)
        
        # Get recent task memories
        recent_tasks = self.memory_manager.get_recent_memories(
            memory_type=MemoryType.TASK,
            hours=24,
            limit=5
        )
        
        return {
            "relevant_memories": [m.content for m in relevant_memories],
            "recent_conversation": [m.content for m in recent_context],
            "recent_tasks": [m.content for m in recent_tasks],
            "session_id": self.session_id
        }

    def create_enhanced_prompt(self, user_request: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create an enhanced system prompt with all available capabilities.

        Args:
            user_request: User's request
            context: Additional context information

        Returns:
            Enhanced system prompt
        """
        # Get available tools
        tool_manager = get_tool_manager()
        available_tools = [tool.name for tool in tool_manager.list_available_tools()]

        # Get user preferences from memory
        user_preferences = self.memory_manager.get_user_preferences()

        # Get personalized suggestions
        suggestions = self.memory_manager.get_personalized_suggestions(user_request)

        # Combine context
        full_context = context or {}
        full_context.update({
            "user_request": user_request,
            "suggestions": suggestions,
            "session_stats": {
                "total_requests": self.total_requests,
                "successful_requests": self.successful_requests,
                "session_duration": (datetime.now() - self.start_time).total_seconds()
            }
        })

        # Create enhanced prompt
        return self.prompt_manager.create_system_prompt(
            context=full_context,
            available_tools=available_tools,
            user_preferences=user_preferences
        )

    async def process_with_learning(
        self,
        user_request: str,
        context: Optional[Dict[str, Any]] = None,
        learn_from_result: bool = True
    ) -> Dict[str, Any]:
        """
        Process request with learning capabilities.

        Args:
            user_request: User's request
            context: Additional context
            learn_from_result: Whether to learn from the result

        Returns:
            Processing result with learning insights
        """
        # Process the request normally
        result = await self.process_request(user_request, context)

        # Learn from the interaction if enabled
        if learn_from_result and self.memory_manager.learning_enabled:
            success = result.get("success", False)
            ai_response = result.get("summary", "")

            self.memory_manager.learn_from_interaction(
                user_input=user_request,
                ai_response=ai_response,
                success=success
            )

        # Add learning insights to result
        if learn_from_result:
            result["learning_insights"] = {
                "patterns_detected": len(self.memory_manager.user_patterns),
                "preferences_learned": len(self.memory_manager.preference_scores),
                "suggestions": self.memory_manager.get_personalized_suggestions(user_request)
            }

        return result
    
    def _process_execution_results(self, tasks: List[Task], results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process execution results into final response."""
        successful_tasks = [r for r in results if r.get("status") == "completed"]
        failed_tasks = [r for r in results if r.get("status") == "failed"]
        
        # Collect all outputs
        outputs = []
        for result in results:
            if result.get("result") and result["result"].get("result"):
                outputs.append(result["result"]["result"])
        
        # Create summary
        if successful_tasks:
            if failed_tasks:
                summary = f"Completed {len(successful_tasks)} of {len(tasks)} tasks successfully."
            else:
                summary = f"All {len(tasks)} tasks completed successfully."
        else:
            summary = f"Failed to complete tasks. {len(failed_tasks)} tasks failed."
        
        # Add specific outputs to summary
        if outputs:
            summary += "\n\nResults:\n" + "\n".join(str(output) for output in outputs[:3])
            if len(outputs) > 3:
                summary += f"\n... and {len(outputs) - 3} more results."
        
        return {
            "success": len(successful_tasks) > 0,
            "summary": summary,
            "tasks": [
                {
                    "id": task.id,
                    "description": task.description,
                    "type": task.task_type.value,
                    "status": task.status.value,
                    "result": task.result
                }
                for task in tasks
            ],
            "results": results,
            "stats": {
                "total_tasks": len(tasks),
                "successful_tasks": len(successful_tasks),
                "failed_tasks": len(failed_tasks),
                "success_rate": len(successful_tasks) / len(tasks) if tasks else 0
            }
        }
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "session_id": self.session_id,
            "uptime_seconds": uptime,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "success_rate": self.successful_requests / self.total_requests if self.total_requests > 0 else 0,
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "memory_stats": self.memory_manager.get_stats(),
            "executor_stats": self.task_executor.get_execution_stats()
        }
    
    def get_conversation_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get conversation history."""
        memories = self.memory_manager.get_conversation_context(limit)
        return [
            {
                "content": memory.content,
                "timestamp": memory.timestamp.isoformat(),
                "metadata": memory.metadata
            }
            for memory in memories
        ]
    
    async def clear_session(self):
        """Clear current session and start fresh."""
        self.logger.info("Clearing agent session")
        
        # Reset state
        self.active_tasks = []
        self.completed_tasks = []
        
        # Clear memory (keep some system memories)
        # TODO: Implement selective memory clearing
        
        # Reinitialize
        await self.initialize()


# Global agent engine instance
_agent_engine: Optional[AgentEngine] = None


def get_agent_engine() -> AgentEngine:
    """Get the global agent engine instance."""
    global _agent_engine
    if _agent_engine is None:
        _agent_engine = AgentEngine()
    return _agent_engine
