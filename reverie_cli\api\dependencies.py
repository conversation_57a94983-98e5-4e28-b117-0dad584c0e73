"""
FastAPI dependencies for the Reverie CLI API.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from reverie_cli.core.config import get_settings
from reverie_cli.core.logging import get_logger


logger = get_logger("dependencies")
security = HTTPBearer(auto_error=False)


async def get_current_settings():
    """Get current application settings."""
    return get_settings()


async def verify_api_key(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """
    Verify API key if authentication is enabled.
    
    Returns:
        API key if valid, None if no authentication required
    """
    # TODO: Implement API key verification
    # For now, allow all requests
    return None


async def get_current_user(api_key: Optional[str] = Depends(verify_api_key)):
    """
    Get current user from API key.

    Returns:
        User information if authenticated
    """
    # TODO: Implement user authentication
    # For now, return anonymous user
    return {"id": "anonymous", "name": "Anonymous User"}


def get_model_manager_dep(request: Request):
    """Get model manager from app state."""
    return request.app.state.model_manager


def get_tool_manager_dep(request: Request):
    """Get tool manager from app state."""
    return request.app.state.tool_manager


def get_agent_engine_dep(request: Request):
    """Get agent engine from app state."""
    return request.app.state.agent_engine


__all__ = [
    "get_current_settings",
    "verify_api_key",
    "get_current_user",
    "get_model_manager_dep",
    "get_tool_manager_dep",
    "get_agent_engine_dep",
]
