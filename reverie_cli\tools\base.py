"""
Base classes for the tool system.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ToolError


class ToolCategory(str, Enum):
    """Tool category enumeration."""
    FILE_SYSTEM = "file_system"
    CODE = "code"
    WEB = "web"
    GIT = "git"
    SYSTEM = "system"
    UTILITY = "utility"
    CONTEXT = "context"
    MEMORY = "memory"


@dataclass
class ToolResult:
    """Result of a tool execution."""
    success: bool
    result: Any = None
    message: str = ""
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "success": self.success,
            "result": self.result,
            "message": self.message,
            "error": self.error,
            "execution_time": self.execution_time,
            "metadata": self.metadata or {}
        }


class ToolParameter(BaseModel):
    """Tool parameter definition."""
    name: str = Field(..., description="Parameter name")
    type: str = Field(..., description="Parameter type")
    description: str = Field(..., description="Parameter description")
    required: bool = Field(True, description="Whether parameter is required")
    default: Any = Field(None, description="Default value")
    choices: Optional[List[str]] = Field(None, description="Valid choices")


class ToolInfo(BaseModel):
    """Tool information model."""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    category: ToolCategory = Field(..., description="Tool category")
    parameters: List[ToolParameter] = Field(default_factory=list, description="Tool parameters")
    examples: Optional[List[Dict[str, Any]]] = Field(None, description="Usage examples")
    version: str = Field("1.0.0", description="Tool version")
    author: Optional[str] = Field(None, description="Tool author")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "category": self.category.value,
            "parameters": [param.dict() for param in self.parameters],
            "examples": self.examples or [],
            "version": self.version,
            "author": self.author
        }


class BaseTool(ABC):
    """
    Base class for all tools.
    
    Tools are the primary way for AI agents to interact with the environment.
    Each tool should inherit from this class and implement the execute method.
    """
    
    def __init__(self):
        self.logger = get_logger(f"tool.{self.name}")
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Tool name."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Tool description."""
        pass
    
    @property
    @abstractmethod
    def category(self) -> ToolCategory:
        """Tool category."""
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> List[ToolParameter]:
        """Tool parameters."""
        pass
    
    @property
    def version(self) -> str:
        """Tool version."""
        return "1.0.0"
    
    @property
    def author(self) -> Optional[str]:
        """Tool author."""
        return None
    
    @property
    def examples(self) -> Optional[List[Dict[str, Any]]]:
        """Usage examples."""
        return None
    
    def get_info(self) -> ToolInfo:
        """Get tool information."""
        return ToolInfo(
            name=self.name,
            description=self.description,
            category=self.category,
            parameters=self.parameters,
            examples=self.examples,
            version=self.version,
            author=self.author
        )
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and process parameters.
        
        Args:
            parameters: Raw parameters
            
        Returns:
            Validated parameters
            
        Raises:
            ToolError: If validation fails
        """
        validated = {}
        
        for param in self.parameters:
            value = parameters.get(param.name)
            
            # Check required parameters
            if param.required and value is None:
                raise ToolError(f"Required parameter '{param.name}' is missing")
            
            # Use default if not provided
            if value is None:
                value = param.default
            
            # Validate choices
            if param.choices and value not in param.choices:
                raise ToolError(
                    f"Parameter '{param.name}' must be one of {param.choices}, got '{value}'"
                )
            
            validated[param.name] = value
        
        return validated
    
    @abstractmethod
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """
        Execute the tool with given parameters.
        
        Args:
            parameters: Tool parameters
            
        Returns:
            Tool execution result
        """
        pass
    
    async def safe_execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """
        Safely execute the tool with error handling.
        
        Args:
            parameters: Tool parameters
            
        Returns:
            Tool execution result
        """
        import time
        start_time = time.time()
        
        try:
            # Validate parameters
            validated_params = self.validate_parameters(parameters)
            
            # Execute tool
            self.logger.info(f"Executing tool {self.name} with parameters: {validated_params}")
            result = await self.execute(validated_params)
            
            # Add execution time
            result.execution_time = time.time() - start_time
            
            self.logger.info(f"Tool {self.name} executed successfully in {result.execution_time:.3f}s")
            return result
            
        except ToolError as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Tool {self.name} validation error: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Tool validation failed: {e}",
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Tool {self.name} execution error: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Tool execution failed: {e}",
                execution_time=execution_time
            )
    
    def __str__(self) -> str:
        return f"Tool({self.name})"
    
    def __repr__(self) -> str:
        return f"Tool(name='{self.name}', category='{self.category.value}')"
