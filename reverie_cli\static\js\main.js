/**
 * Reverie CLI Web Interface JavaScript
 */

// Global state
let currentModel = null;
let chatHistory = [];
let isTyping = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    updateSystemStatus();
    
    // Update status every 30 seconds
    setInterval(updateSystemStatus, 30000);
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('🚀 Reverie CLI Web Interface initialized');
    
    // Load current model
    loadCurrentModel();
    
    // Setup event listeners
    setupEventListeners();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Chat input enter key
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.addEventListener('keypress', handleChatKeyPress);
    }
}

/**
 * Handle chat key press
 */
function handleChatKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

/**
 * Open chat interface
 */
function openChat() {
    const chatSection = document.getElementById('chat-section');
    if (chatSection) {
        chatSection.classList.remove('hidden');
        chatSection.scrollIntoView({ behavior: 'smooth' });
        
        // Focus on input
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.focus();
        }
    }
}

/**
 * Close chat interface
 */
function closeChat() {
    const chatSection = document.getElementById('chat-section');
    if (chatSection) {
        chatSection.classList.add('hidden');
    }
}

/**
 * Send chat message
 */
async function sendMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    
    if (!message || isTyping) return;
    
    // Clear input
    chatInput.value = '';
    
    // Add user message to chat
    addMessageToChat('user', message);
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Send message to API
        const response = await fetch('/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                messages: [
                    ...chatHistory,
                    { role: 'user', content: message }
                ],
                temperature: 0.7,
                max_tokens: 4096
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        const assistantMessage = data.choices[0].message.content;
        
        // Remove typing indicator
        hideTypingIndicator();
        
        // Add assistant response to chat
        addMessageToChat('assistant', assistantMessage);
        
        // Update chat history
        chatHistory.push(
            { role: 'user', content: message },
            { role: 'assistant', content: assistantMessage }
        );
        
    } catch (error) {
        console.error('Chat error:', error);
        hideTypingIndicator();
        addMessageToChat('assistant', `Sorry, I encountered an error: ${error.message}`);
    }
}

/**
 * Add message to chat
 */
function addMessageToChat(role, content) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message mb-4';
    
    const isUser = role === 'user';
    const avatar = isUser ? '👤' : '🤖';
    const name = isUser ? 'You' : 'AI Assistant';
    const bgColor = isUser ? 'bg-blue-600' : 'bg-green-600';
    
    messageDiv.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 ${bgColor} rounded-full flex items-center justify-center">
                ${avatar}
            </div>
            <div class="flex-1">
                <p class="text-sm text-gray-400 mb-1">${name}</p>
                <div class="text-white">${formatMessage(content)}</div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Format message content
 */
function formatMessage(content) {
    // Convert code blocks
    content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
        return `<pre class="code-block p-3 rounded mt-2 mb-2 overflow-x-auto"><code>${escapeHtml(code.trim())}</code></pre>`;
    });
    
    // Convert inline code
    content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-700 px-1 rounded">$1</code>');
    
    // Convert line breaks
    content = content.replace(/\n/g, '<br>');
    
    return content;
}

/**
 * Escape HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show typing indicator
 */
function showTypingIndicator() {
    isTyping = true;
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    const typingDiv = document.createElement('div');
    typingDiv.id = 'typing-indicator';
    typingDiv.className = 'chat-message mb-4';

    typingDiv.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                🤖
            </div>
            <div class="flex-1">
                <p class="text-sm text-gray-400 mb-1">AI Assistant</p>
                <div class="text-white">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Hide typing indicator
 */
function hideTypingIndicator() {
    isTyping = false;
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

/**
 * Clear chat
 */
function clearChat() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.innerHTML = '';
        chatHistory = [];

        // Add welcome message back
        addMessageToChat('assistant', 'Chat cleared. How can I help you today?');
    }
}

/**
 * Load current model
 */
async function loadCurrentModel() {
    try {
        const response = await fetch('/api/v1/models');
        const data = await response.json();

        if (data.models && data.models.length > 0) {
            currentModel = data.models.find(m => m.status === 'loaded') || data.models[0];
            updateModelDisplay();
        }
    } catch (error) {
        console.error('Failed to load models:', error);
    }
}

/**
 * Update model display
 */
function updateModelDisplay() {
    const modelStatus = document.getElementById('model-status');
    if (modelStatus && currentModel) {
        modelStatus.textContent = `${currentModel.name} Ready`;
    }
}

/**
 * Update system status
 */
async function updateSystemStatus() {
    try {
        const response = await fetch('/api/v1/health');
        const data = await response.json();

        // Update connection status
        const statusIndicators = document.querySelectorAll('.w-3.h-3.rounded-full');
        statusIndicators.forEach(indicator => {
            if (data.status === 'healthy') {
                indicator.className = 'w-3 h-3 bg-green-400 rounded-full animate-pulse';
            } else {
                indicator.className = 'w-3 h-3 bg-red-400 rounded-full';
            }
        });

    } catch (error) {
        console.error('Failed to update system status:', error);

        // Show offline status
        const statusIndicators = document.querySelectorAll('.w-3.h-3.rounded-full');
        statusIndicators.forEach(indicator => {
            indicator.className = 'w-3 h-3 bg-red-400 rounded-full';
        });
    }
}

// Enhanced functions for new features

/**
 * Handle input keydown
 */
function handleInputKeydown(event) {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        sendMessage();
    }
}

/**
 * Quick action handler
 */
function quickAction(action) {
    const chatInput = document.getElementById('chat-input');
    if (!chatInput) return;

    const actions = {
        'analyze-project': 'Analyze the current project structure and provide insights',
        'search-web': 'Search the web for information about: ',
        'generate-code': 'Generate code for: ',
        'review-code': 'Review and analyze the following code for improvements',
        'explain-concept': 'Explain the concept of: '
    };

    const prompt = actions[action] || '';
    chatInput.value = prompt;
    chatInput.focus();

    if (action === 'analyze-project') {
        sendMessage();
    }
}

/**
 * Show models modal
 */
function showModels() {
    alert('Model management interface coming soon!');
}

/**
 * Show console
 */
function showConsole() {
    alert('Console interface coming soon!');
}

/**
 * Show agents
 */
function showAgents() {
    alert('Agent management interface coming soon!');
}

/**
 * Toggle theme
 */
function toggleTheme() {
    console.log('Theme switching coming soon!');
}

/**
 * Attach file
 */
function attachFile() {
    alert('File attachment coming soon!');
}

/**
 * Toggle voice input
 */
function toggleVoice() {
    alert('Voice input coming soon!');
}

// Code editor functions

/**
 * Initialize code editor
 */
function initializeCodeEditor() {
    // Basic syntax highlighting could be added here
    console.log('Code editor initialized');
}

/**
 * Run code
 */
async function runCode() {
    const codeEditor = document.getElementById('code-editor');
    const languageSelect = document.getElementById('language-select');
    const codeOutput = document.getElementById('code-output');

    if (!codeEditor || !languageSelect || !codeOutput) return;

    const code = codeEditor.value;
    const language = languageSelect.value;

    if (!code.trim()) {
        codeOutput.innerHTML = '<div class="text-red-400">No code to execute</div>';
        return;
    }

    codeOutput.innerHTML = '<div class="text-yellow-400">Executing...</div>';

    try {
        const response = await fetch('/api/v1/tools/execute', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                tool_name: language === 'python' ? 'python_executor' : 'shell_executor',
                parameters: {
                    code: code,
                    language: language
                }
            })
        });

        const data = await response.json();

        if (data.success) {
            codeOutput.innerHTML = `<pre class="text-green-400">${escapeHtml(data.result || 'Code executed successfully')}</pre>`;
        } else {
            codeOutput.innerHTML = `<pre class="text-red-400">Error: ${escapeHtml(data.error || 'Execution failed')}</pre>`;
        }
    } catch (error) {
        codeOutput.innerHTML = `<pre class="text-red-400">Error: ${escapeHtml(error.message)}</pre>`;
    }
}

/**
 * Save code
 */
function saveCode() {
    alert('Code saving coming soon!');
}

/**
 * Format code
 */
function formatCode() {
    alert('Code formatting coming soon!');
}

/**
 * Analyze code
 */
function analyzeCode() {
    const codeEditor = document.getElementById('code-editor');
    if (!codeEditor) return;

    const code = codeEditor.value;
    if (!code.trim()) return;

    // Add to chat for analysis
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.value = `Please analyze this code and provide suggestions for improvement:\n\n\`\`\`\n${code}\n\`\`\``;
        openChat();
    }
}

/**
 * Clear output
 */
function clearOutput() {
    const codeOutput = document.getElementById('code-output');
    if (codeOutput) {
        codeOutput.innerHTML = '<div class="text-gray-500">Output will appear here...</div>';
    }
}

// Project explorer functions

/**
 * Load file tree
 */
async function loadFileTree() {
    const fileTree = document.getElementById('file-tree');
    if (!fileTree) return;

    fileTree.innerHTML = '<div class="text-yellow-400">Loading...</div>';

    try {
        const response = await fetch('/api/v1/files/tree');
        const data = await response.json();

        if (data.success) {
            fileTree.innerHTML = renderFileTree(data.tree);
        } else {
            fileTree.innerHTML = '<div class="text-red-400">Failed to load file tree</div>';
        }
    } catch (error) {
        fileTree.innerHTML = '<div class="text-red-400">Error loading file tree</div>';
    }
}

/**
 * Render file tree
 */
function renderFileTree(tree, level = 0) {
    if (!tree || typeof tree !== 'object') return '';

    let html = '';
    const indent = '  '.repeat(level);

    for (const [name, item] of Object.entries(tree.children || {})) {
        if (item.type === 'directory') {
            html += `<div class="text-blue-400">${indent}📁 ${name}</div>`;
            html += renderFileTree(item, level + 1);
        } else {
            html += `<div class="text-gray-300 cursor-pointer hover:text-white" onclick="openFile('${name}')">${indent}📄 ${name}</div>`;
        }
    }

    return html;
}

/**
 * Open file
 */
function openFile(filename) {
    console.log('Opening file:', filename);
    // TODO: Implement file opening
}

/**
 * Refresh project
 */
function refreshProject() {
    loadFileTree();
}

/**
 * Analyze project
 */
function analyzeProject() {
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.value = 'Analyze the current project structure, dependencies, and provide insights about the codebase';
        openChat();
        sendMessage();
    }
}
    if (!chatMessages) return;
    
    const typingDiv = document.createElement('div');
    typingDiv.id = 'typing-indicator';
    typingDiv.className = 'chat-message mb-4';
    typingDiv.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                🤖
            </div>
            <div class="flex-1">
                <p class="text-sm text-gray-400 mb-1">AI Assistant</p>
                <div class="text-white typing-indicator">
                    <span class="inline-block w-2 h-2 bg-gray-400 rounded-full mr-1 animate-bounce"></span>
                    <span class="inline-block w-2 h-2 bg-gray-400 rounded-full mr-1 animate-bounce" style="animation-delay: 0.1s"></span>
                    <span class="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></span>
                </div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Hide typing indicator
 */
function hideTypingIndicator() {
    isTyping = false;
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

/**
 * Update system status
 */
async function updateSystemStatus() {
    try {
        const response = await fetch('/api/v1/health/detailed');
        const data = await response.json();
        
        // Update status indicators
        document.getElementById('server-status').textContent = 'Online';
        document.getElementById('model-status').textContent = currentModel ? 'Loaded' : 'Ready';
        document.getElementById('agent-status').textContent = 'Active';
        document.getElementById('memory-usage').textContent = `${data.system.memory.percent.toFixed(1)}%`;
        
    } catch (error) {
        console.error('Failed to update system status:', error);
        document.getElementById('server-status').textContent = 'Error';
    }
}

/**
 * Load current model
 */
async function loadCurrentModel() {
    try {
        const response = await fetch('/api/v1/models/current');
        const data = await response.json();
        currentModel = data.current_model;
    } catch (error) {
        console.error('Failed to load current model:', error);
    }
}

/**
 * Show models dialog
 */
function showModels() {
    alert('Models management coming soon! Use the CLI console for now.');
}

/**
 * Show console dialog
 */
function showConsole() {
    alert('Console integration coming soon! Use the terminal for now.');
}

/**
 * Show agents dialog
 */
function showAgents() {
    alert('Agent management coming soon! Use the API endpoints for now.');
}

/**
 * Toggle theme (placeholder)
 */
function toggleTheme() {
    console.log('Theme toggle clicked');
    // TODO: Implement theme switching
}

/**
 * Utility functions
 */

// Format file size
function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Format duration
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs}s`;
    } else {
        return `${secs}s`;
    }
}

// Show notification
function showNotification(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    // TODO: Implement proper notification system
}
