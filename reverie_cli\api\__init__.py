"""
API module for Reverie CLI.

This module contains the FastAPI application and all REST API endpoints
for the Reverie CLI server, including:
- Health checks and system status
- Model management endpoints
- Chat and completion APIs
- Agent interaction endpoints
- File and tool management
- WebSocket connections for real-time communication
"""

from reverie_cli.api.server import create_app

__all__ = [
    "create_app",
]
