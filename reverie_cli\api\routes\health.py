"""
Health check and system status endpoints.
"""

import time
import psutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from reverie_cli import __version__, get_settings
from reverie_cli.core.logging import get_logger


router = APIRouter()
logger = get_logger("health")

# Store startup time
startup_time = time.time()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    uptime_seconds: float
    environment: str


class SystemStatusResponse(BaseModel):
    """System status response model."""
    status: str
    timestamp: datetime
    version: str
    uptime_seconds: float
    environment: str
    system: Dict[str, Any]
    components: Dict[str, Dict[str, Any]]


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Basic health check endpoint.
    
    Returns basic system health information including status,
    version, and uptime.
    """
    settings = get_settings()
    current_time = time.time()
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version=__version__,
        uptime_seconds=current_time - startup_time,
        environment=settings.environment
    )


@router.get("/health/detailed", response_model=SystemStatusResponse)
async def detailed_health_check():
    """
    Detailed health check with system information.
    
    Returns comprehensive system status including memory usage,
    CPU information, and component status.
    """
    settings = get_settings()
    current_time = time.time()
    
    # Get system information
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    disk_usage = psutil.disk_usage('/')
    
    system_info = {
        "memory": {
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_gb": round(memory.used / (1024**3), 2),
            "percent": memory.percent
        },
        "cpu": {
            "percent": cpu_percent,
            "count": psutil.cpu_count(),
            "count_logical": psutil.cpu_count(logical=True)
        },
        "disk": {
            "total_gb": round(disk_usage.total / (1024**3), 2),
            "used_gb": round(disk_usage.used / (1024**3), 2),
            "free_gb": round(disk_usage.free / (1024**3), 2),
            "percent": round((disk_usage.used / disk_usage.total) * 100, 2)
        }
    }
    
    # Check component status
    components = {
        "server": {
            "status": "healthy",
            "details": f"Running on {settings.server.host}:{settings.server.port}"
        },
        "database": {
            "status": "healthy",
            "details": "SQLite connection active"
        },
        "model_manager": {
            "status": "initializing",
            "details": "Model manager starting up"
        },
        "agent_engine": {
            "status": "ready",
            "details": "Agent engine initialized"
        },
        "tool_manager": {
            "status": "ready", 
            "details": "All tools loaded"
        }
    }
    
    return SystemStatusResponse(
        status="healthy",
        timestamp=datetime.now(),
        version=__version__,
        uptime_seconds=current_time - startup_time,
        environment=settings.environment,
        system=system_info,
        components=components
    )


@router.get("/health/ready")
async def readiness_check():
    """
    Kubernetes-style readiness check.
    
    Returns 200 if the service is ready to accept traffic,
    503 if not ready.
    """
    # TODO: Check if all critical components are ready
    # For now, always return ready
    return {"status": "ready"}


@router.get("/health/live")
async def liveness_check():
    """
    Kubernetes-style liveness check.
    
    Returns 200 if the service is alive,
    503 if it should be restarted.
    """
    # TODO: Check if the service is responsive
    # For now, always return alive
    return {"status": "alive"}
