"""
Enhanced Context Engine for Reverie CLI.

This module provides advanced context understanding and analysis capabilities
similar to Augment's context engine. It includes:
- Deep codebase analysis and understanding with AI-powered insights
- Advanced dependency tracking and relationship mapping
- Semantic code search and intelligent retrieval
- Project structure analysis with pattern recognition
- Code quality assessment and improvement suggestions
- Memory-enhanced context understanding
- One-line function calls for complex operations
- Real-time code intelligence and monitoring
"""

import asyncio
import ast
import os
import re
import json
import sqlite3
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import subprocess

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings
from reverie_cli.tools.base import BaseTool, ToolResult, ToolError, ToolCategory, ToolParameter


@dataclass
class CodeSymbol:
    """Enhanced representation of a code symbol with AI-powered insights."""
    name: str
    type: str  # function, class, variable, import, etc.
    file_path: str
    line_number: int
    definition: str
    docstring: Optional[str] = None
    dependencies: List[str] = None
    usage_count: int = 0
    complexity_score: float = 0.0
    quality_score: float = 0.0
    last_modified: Optional[datetime] = None
    semantic_tags: List[str] = None
    ai_summary: Optional[str] = None


@dataclass
class CodeRelationship:
    """Enhanced representation of relationships between code symbols."""
    source: str
    target: str
    relationship_type: str  # calls, inherits, imports, etc.
    file_path: str
    line_number: int
    strength: float = 1.0  # Relationship strength (0.0 to 1.0)
    context: Optional[str] = None
    last_seen: Optional[datetime] = None


@dataclass
class ProjectContext:
    """Enhanced representation of the overall project context."""
    root_path: str
    language: str
    framework: Optional[str]
    symbols: Dict[str, CodeSymbol]
    relationships: List[CodeRelationship]
    file_tree: Dict[str, Any]
    dependencies: Dict[str, str]
    patterns: List[str]
    last_updated: datetime
    quality_metrics: Dict[str, float] = None
    ai_insights: Dict[str, Any] = None
    change_history: List[Dict[str, Any]] = None


@dataclass
class CodeInsight:
    """AI-powered code insight and suggestion."""
    type: str  # suggestion, warning, optimization, etc.
    severity: str  # low, medium, high, critical
    message: str
    file_path: str
    line_number: int
    suggestion: Optional[str] = None
    confidence: float = 0.0
    tags: List[str] = None


class ContextEngine(BaseTool):
    """
    Enhanced Context Engine for intelligent code understanding with AI capabilities.

    Provides deep analysis of codebases, dependency tracking, semantic understanding,
    and AI-powered insights to enable intelligent development assistance.
    """

    def __init__(self):
        self._name = "context_engine"
        self._description = "Enhanced context analysis and code understanding engine with AI capabilities"
        super().__init__()
        self.logger = get_logger("context_engine")
        self.settings = get_settings()

    @property
    def name(self) -> str:
        """Get the tool name."""
        return "context_engine"

    @property
    def description(self) -> str:
        """Get the tool description."""
        return "Enhanced codebase analysis and context understanding with AI insights"

    @property
    def category(self) -> ToolCategory:
        """Get the tool category."""
        return ToolCategory.CONTEXT

    @property
    def parameters(self) -> List[ToolParameter]:
        """Get the tool parameters schema."""
        return [
            ToolParameter(
                name="action",
                type="string",
                description="Action to perform",
                required=False,
                default="analyze",
                choices=["analyze", "quality", "security"]
            ),
            ToolParameter(
                name="project_path",
                type="string",
                description="Path to analyze",
                required=False,
                default="."
            ),
            ToolParameter(
                name="analysis_type",
                type="string",
                description="Type of analysis",
                required=False,
                default="quick",
                choices=["quick", "comprehensive"]
            ),
            ToolParameter(
                name="include_ai_insights",
                type="boolean",
                description="Include AI insights",
                required=False,
                default=True
            )
        ]

    def __init__(self):
        """Initialize the enhanced context engine."""
        super().__init__()
        self.logger = get_logger("context_engine")
        self.settings = get_settings()

        # Initialize persistent storage
        self.db_path = Path("data/context_cache.db")
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()

        # Enhanced context cache with memory
        self.project_contexts: Dict[str, ProjectContext] = {}
        self.symbol_index: Dict[str, List[CodeSymbol]] = defaultdict(list)
        self.relationship_graph: Dict[str, List[CodeRelationship]] = defaultdict(list)
        self.insights_cache: Dict[str, List[CodeInsight]] = defaultdict(list)

        # Enhanced language parsers with AI capabilities
        self.language_parsers = {
            ".py": self._parse_python_file_enhanced,
            ".js": self._parse_javascript_file_enhanced,
            ".ts": self._parse_typescript_file_enhanced,
            ".java": self._parse_java_file_enhanced,
            ".cpp": self._parse_cpp_file_enhanced,
            ".c": self._parse_c_file_enhanced,
            ".go": self._parse_go_file_enhanced,
            ".rs": self._parse_rust_file_enhanced,
            ".rb": self._parse_ruby_file_enhanced,
            ".php": self._parse_php_file_enhanced,
            ".cs": self._parse_csharp_file_enhanced,
        }

        # Enhanced file patterns to ignore
        self.ignore_patterns = {
            "__pycache__", ".git", ".vscode", ".idea", "node_modules",
            ".pytest_cache", ".mypy_cache", "dist", "build", ".env",
            ".DS_Store", "*.pyc", "*.pyo", "*.pyd", ".coverage",
            "venv", "env", ".venv", ".env", "target", "bin", "obj"
        }

        # Code quality patterns and metrics
        self.quality_patterns = {
            "code_smells": [
                r"def\s+\w+\([^)]*\):\s*\n(\s*.*\n){50,}",  # Long functions
                r"class\s+\w+.*:\s*\n(\s*.*\n){200,}",      # Large classes
                r"if\s+.*:\s*\n(\s*if\s+.*:\s*\n){5,}",     # Deep nesting
            ],
            "security_issues": [
                r"eval\s*\(",                               # eval usage
                r"exec\s*\(",                               # exec usage
                r"subprocess\.call\s*\(",                   # subprocess without shell=False
                r"os\.system\s*\(",                         # os.system usage
            ],
            "performance_issues": [
                r"for\s+\w+\s+in\s+range\(len\(",          # range(len()) pattern
                r"\.append\s*\([^)]*\)\s*\n(\s*.*\.append\s*\([^)]*\)\s*\n){10,}",  # Multiple appends
            ]
        }

        # AI-powered analysis settings
        self.analysis_cache_ttl = timedelta(hours=24)
        self.max_analysis_cache_size = 10000

        self.logger.info("Enhanced ContextEngine initialized with AI capabilities and persistent storage")

    def _init_database(self):
        """Initialize SQLite database for persistent context storage."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Symbols table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS symbols (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_path TEXT,
                        name TEXT,
                        type TEXT,
                        file_path TEXT,
                        line_number INTEGER,
                        definition TEXT,
                        docstring TEXT,
                        complexity_score REAL,
                        quality_score REAL,
                        semantic_tags TEXT,
                        ai_summary TEXT,
                        last_modified DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Relationships table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS relationships (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_path TEXT,
                        source TEXT,
                        target TEXT,
                        relationship_type TEXT,
                        file_path TEXT,
                        line_number INTEGER,
                        strength REAL,
                        context TEXT,
                        last_seen DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Insights table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS insights (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_path TEXT,
                        type TEXT,
                        severity TEXT,
                        message TEXT,
                        file_path TEXT,
                        line_number INTEGER,
                        suggestion TEXT,
                        confidence REAL,
                        tags TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Project metadata table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS project_metadata (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_path TEXT UNIQUE,
                        language TEXT,
                        framework TEXT,
                        quality_metrics TEXT,
                        ai_insights TEXT,
                        last_analyzed DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create indices for better performance
                conn.execute("CREATE INDEX IF NOT EXISTS idx_symbols_project ON symbols(project_path)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_symbols_name ON symbols(name)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_relationships_project ON relationships(project_path)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_insights_project ON insights(project_path)")

        except Exception as e:
            self.logger.error(f"Failed to initialize context database: {e}")

    async def smart_analyze(
        self,
        project_path: str,
        analysis_type: str = "comprehensive",
        include_ai_insights: bool = True
    ) -> ToolResult:
        """
        One-line smart project analysis with AI-powered insights.

        Args:
            project_path: Path to the project root
            analysis_type: Type of analysis ("quick", "comprehensive", "security", "performance")
            include_ai_insights: Include AI-powered insights and suggestions
        """
        try:
            self.logger.info(f"Smart analysis of project: {project_path}")

            project_path = os.path.abspath(project_path)

            # Check if we have recent analysis
            if analysis_type != "comprehensive":
                cached_result = await self._get_cached_analysis(project_path, analysis_type)
                if cached_result:
                    return cached_result

            # Perform enhanced analysis
            context = await self._perform_smart_analysis(project_path, analysis_type, include_ai_insights)

            # Cache results
            await self._cache_analysis_results(project_path, context, analysis_type)

            return ToolResult(
                success=True,
                data=self._serialize_enhanced_context(context),
                message=f"Smart {analysis_type} analysis completed: {len(context.symbols)} symbols analyzed"
            )

        except Exception as e:
            self.logger.error(f"Smart analysis failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Smart analysis failed"
            )

    async def analyze_project(
        self,
        project_path: str,
        force_refresh: bool = False,
        include_dependencies: bool = True
    ) -> ToolResult:
        """
        Analyze a project and build comprehensive context.
        
        Args:
            project_path: Path to the project root
            force_refresh: Force re-analysis even if cached
            include_dependencies: Include external dependencies analysis
        """
        try:
            self.logger.info(f"Analyzing project: {project_path}")
            
            project_path = os.path.abspath(project_path)
            
            # Check if we have cached context
            if not force_refresh and project_path in self.project_contexts:
                context = self.project_contexts[project_path]
                if (datetime.now() - context.last_updated).seconds < 3600:  # 1 hour cache
                    self.logger.info("Using cached project context")
                    return ToolResult(
                        success=True,
                        data=self._serialize_context(context),
                        message="Project context retrieved (cached)"
                    )
            
            # Detect project language and framework
            language, framework = await self._detect_project_type(project_path)
            
            # Build file tree
            file_tree = await self._build_file_tree(project_path)
            
            # Parse source files
            symbols = {}
            relationships = []
            
            for file_path in self._get_source_files(project_path):
                try:
                    file_symbols, file_relationships = await self._parse_file(file_path)
                    symbols.update(file_symbols)
                    relationships.extend(file_relationships)
                except Exception as e:
                    self.logger.warning(f"Failed to parse {file_path}: {e}")
            
            # Analyze dependencies
            dependencies = {}
            if include_dependencies:
                dependencies = await self._analyze_dependencies(project_path, language)
            
            # Detect patterns
            patterns = await self._detect_patterns(symbols, relationships)
            
            # Create project context
            context = ProjectContext(
                root_path=project_path,
                language=language,
                framework=framework,
                symbols=symbols,
                relationships=relationships,
                file_tree=file_tree,
                dependencies=dependencies,
                patterns=patterns,
                last_updated=datetime.now()
            )
            
            # Cache context
            self.project_contexts[project_path] = context
            
            # Update indices
            await self._update_indices(context)
            
            return ToolResult(
                success=True,
                data=self._serialize_context(context),
                message=f"Project analyzed: {len(symbols)} symbols, {len(relationships)} relationships"
            )
            
        except Exception as e:
            self.logger.error(f"Project analysis failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Project analysis failed"
            )
    
    async def search_symbols(
        self,
        query: str,
        symbol_type: Optional[str] = None,
        project_path: Optional[str] = None,
        max_results: int = 20
    ) -> ToolResult:
        """
        Search for code symbols using semantic search.
        
        Args:
            query: Search query
            symbol_type: Filter by symbol type (function, class, etc.)
            project_path: Limit search to specific project
            max_results: Maximum number of results
        """
        try:
            self.logger.info(f"Searching symbols for: {query}")
            
            results = []
            search_contexts = []
            
            if project_path:
                if project_path in self.project_contexts:
                    search_contexts = [self.project_contexts[project_path]]
            else:
                search_contexts = list(self.project_contexts.values())
            
            for context in search_contexts:
                for symbol_name, symbol in context.symbols.items():
                    # Calculate relevance score
                    score = self._calculate_symbol_relevance(query, symbol)
                    
                    if score > 0.1:  # Minimum relevance threshold
                        if not symbol_type or symbol.type == symbol_type:
                            results.append({
                                "symbol": symbol_name,
                                "type": symbol.type,
                                "file_path": symbol.file_path,
                                "line_number": symbol.line_number,
                                "definition": symbol.definition,
                                "docstring": symbol.docstring,
                                "score": score,
                                "project": context.root_path
                            })
            
            # Sort by relevance
            results.sort(key=lambda x: x["score"], reverse=True)
            
            return ToolResult(
                success=True,
                data=results[:max_results],
                message=f"Found {len(results)} matching symbols"
            )
            
        except Exception as e:
            self.logger.error(f"Symbol search failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Symbol search failed"
            )
    
    async def find_dependencies(
        self,
        symbol_name: str,
        project_path: Optional[str] = None,
        depth: int = 3
    ) -> ToolResult:
        """
        Find dependencies and dependents of a symbol.
        
        Args:
            symbol_name: Name of the symbol
            project_path: Project to search in
            depth: Maximum depth for dependency traversal
        """
        try:
            self.logger.info(f"Finding dependencies for: {symbol_name}")
            
            if project_path and project_path not in self.project_contexts:
                return ToolResult(
                    success=False,
                    error="Project not analyzed",
                    message="Project context not found"
                )
            
            context = self.project_contexts.get(project_path) if project_path else None
            
            dependencies = await self._traverse_dependencies(
                symbol_name, context, depth, set()
            )
            
            dependents = await self._traverse_dependents(
                symbol_name, context, depth, set()
            )
            
            return ToolResult(
                success=True,
                data={
                    "symbol": symbol_name,
                    "dependencies": dependencies,
                    "dependents": dependents,
                    "dependency_count": len(dependencies),
                    "dependent_count": len(dependents)
                },
                message=f"Found {len(dependencies)} dependencies and {len(dependents)} dependents"
            )
            
        except Exception as e:
            self.logger.error(f"Dependency analysis failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Dependency analysis failed"
            )
    
    async def analyze_code_quality(
        self,
        file_path: str,
        include_suggestions: bool = True
    ) -> ToolResult:
        """
        Analyze code quality and provide suggestions.
        
        Args:
            file_path: Path to the file to analyze
            include_suggestions: Include improvement suggestions
        """
        try:
            self.logger.info(f"Analyzing code quality for: {file_path}")
            
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error="File not found",
                    message="File does not exist"
                )
            
            # Parse file
            symbols, relationships = await self._parse_file(file_path)
            
            # Analyze metrics
            metrics = await self._calculate_code_metrics(file_path, symbols)
            
            # Detect issues
            issues = await self._detect_code_issues(file_path, symbols, relationships)
            
            # Generate suggestions
            suggestions = []
            if include_suggestions:
                suggestions = await self._generate_suggestions(file_path, symbols, issues)
            
            return ToolResult(
                success=True,
                data={
                    "file_path": file_path,
                    "metrics": metrics,
                    "issues": issues,
                    "suggestions": suggestions,
                    "quality_score": self._calculate_quality_score(metrics, issues)
                },
                message=f"Code quality analysis complete: {len(issues)} issues found"
            )
            
        except Exception as e:
            self.logger.error(f"Code quality analysis failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Code quality analysis failed"
            )

    async def _detect_project_type(self, project_path: str) -> Tuple[str, Optional[str]]:
        """Detect project language and framework."""
        files = os.listdir(project_path)

        # Check for specific files that indicate language/framework
        if "package.json" in files:
            return "javascript", self._detect_js_framework(project_path)
        elif "requirements.txt" in files or "pyproject.toml" in files or "setup.py" in files:
            return "python", self._detect_python_framework(project_path)
        elif "pom.xml" in files or "build.gradle" in files:
            return "java", None
        elif "Cargo.toml" in files:
            return "rust", None
        elif "go.mod" in files:
            return "go", None
        elif "CMakeLists.txt" in files or "Makefile" in files:
            return "cpp", None

        # Fallback: detect by file extensions
        extensions = set()
        for file in files:
            if os.path.isfile(os.path.join(project_path, file)):
                ext = os.path.splitext(file)[1]
                extensions.add(ext)

        if ".py" in extensions:
            return "python", None
        elif ".js" in extensions or ".ts" in extensions:
            return "javascript", None
        elif ".java" in extensions:
            return "java", None
        elif ".cpp" in extensions or ".c" in extensions:
            return "cpp", None
        elif ".go" in extensions:
            return "go", None
        elif ".rs" in extensions:
            return "rust", None

        return "unknown", None

    def _detect_js_framework(self, project_path: str) -> Optional[str]:
        """Detect JavaScript framework."""
        try:
            package_json_path = os.path.join(project_path, "package.json")
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)

            dependencies = {**package_data.get("dependencies", {}), **package_data.get("devDependencies", {})}

            if "react" in dependencies:
                return "react"
            elif "vue" in dependencies:
                return "vue"
            elif "angular" in dependencies or "@angular/core" in dependencies:
                return "angular"
            elif "express" in dependencies:
                return "express"
            elif "next" in dependencies:
                return "nextjs"

        except Exception:
            pass

        return None

    def _detect_python_framework(self, project_path: str) -> Optional[str]:
        """Detect Python framework."""
        # Check for common framework files
        if os.path.exists(os.path.join(project_path, "manage.py")):
            return "django"
        elif os.path.exists(os.path.join(project_path, "app.py")) or os.path.exists(os.path.join(project_path, "main.py")):
            # Check for FastAPI/Flask imports
            try:
                for file in ["app.py", "main.py"]:
                    file_path = os.path.join(project_path, file)
                    if os.path.exists(file_path):
                        with open(file_path, 'r') as f:
                            content = f.read()
                        if "from fastapi" in content or "import fastapi" in content:
                            return "fastapi"
                        elif "from flask" in content or "import flask" in content:
                            return "flask"
            except Exception:
                pass

        return None

    async def _build_file_tree(self, project_path: str) -> Dict[str, Any]:
        """Build a tree structure of the project files."""
        def build_tree(path: str) -> Dict[str, Any]:
            tree = {"type": "directory", "children": {}}

            try:
                for item in os.listdir(path):
                    if item.startswith('.') or item in self.ignore_patterns:
                        continue

                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        tree["children"][item] = build_tree(item_path)
                    else:
                        tree["children"][item] = {
                            "type": "file",
                            "size": os.path.getsize(item_path),
                            "extension": os.path.splitext(item)[1]
                        }
            except PermissionError:
                pass

            return tree

        return build_tree(project_path)

    def _get_source_files(self, project_path: str) -> List[str]:
        """Get list of source files to parse."""
        source_files = []

        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignore_patterns]

            for file in files:
                file_path = os.path.join(root, file)
                ext = os.path.splitext(file)[1]

                if ext in self.language_parsers:
                    source_files.append(file_path)

        return source_files

    async def _parse_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a source file and extract symbols and relationships."""
        ext = os.path.splitext(file_path)[1]

        if ext in self.language_parsers:
            return await self.language_parsers[ext](file_path)
        else:
            return {}, []

    async def _parse_python_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a Python file."""
        symbols = {}
        relationships = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    symbol_name = f"{file_path}::{node.name}"
                    symbols[symbol_name] = CodeSymbol(
                        name=node.name,
                        type="function",
                        file_path=file_path,
                        line_number=node.lineno,
                        definition=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node)
                    )

                elif isinstance(node, ast.ClassDef):
                    symbol_name = f"{file_path}::{node.name}"
                    symbols[symbol_name] = CodeSymbol(
                        name=node.name,
                        type="class",
                        file_path=file_path,
                        line_number=node.lineno,
                        definition=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node)
                    )

                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        symbol_name = f"{file_path}::import::{alias.name}"
                        symbols[symbol_name] = CodeSymbol(
                            name=alias.name,
                            type="import",
                            file_path=file_path,
                            line_number=node.lineno,
                            definition=f"import {alias.name}"
                        )

                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            symbol_name = f"{file_path}::import::{node.module}.{alias.name}"
                            symbols[symbol_name] = CodeSymbol(
                                name=f"{node.module}.{alias.name}",
                                type="import",
                                file_path=file_path,
                                line_number=node.lineno,
                                definition=f"from {node.module} import {alias.name}"
                            )

        except Exception as e:
            self.logger.warning(f"Failed to parse Python file {file_path}: {e}")

        return symbols, relationships

    async def _parse_javascript_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a JavaScript file (basic implementation)."""
        symbols = {}
        relationships = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Simple regex-based parsing for JavaScript
            # Function declarations
            function_pattern = r'function\s+(\w+)\s*\('
            for match in re.finditer(function_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                symbol_name = f"{file_path}::{match.group(1)}"
                symbols[symbol_name] = CodeSymbol(
                    name=match.group(1),
                    type="function",
                    file_path=file_path,
                    line_number=line_num,
                    definition=match.group(0)
                )

            # Class declarations
            class_pattern = r'class\s+(\w+)'
            for match in re.finditer(class_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                symbol_name = f"{file_path}::{match.group(1)}"
                symbols[symbol_name] = CodeSymbol(
                    name=match.group(1),
                    type="class",
                    file_path=file_path,
                    line_number=line_num,
                    definition=match.group(0)
                )

            # Import statements
            import_pattern = r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]'
            for match in re.finditer(import_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                symbol_name = f"{file_path}::import::{match.group(1)}"
                symbols[symbol_name] = CodeSymbol(
                    name=match.group(1),
                    type="import",
                    file_path=file_path,
                    line_number=line_num,
                    definition=match.group(0)
                )

        except Exception as e:
            self.logger.warning(f"Failed to parse JavaScript file {file_path}: {e}")

        return symbols, relationships

    # Placeholder implementations for other language parsers
    async def _parse_typescript_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a TypeScript file (similar to JavaScript)."""
        return await self._parse_javascript_file(file_path)

    async def _parse_java_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a Java file (basic implementation)."""
        return {}, []

    async def _parse_cpp_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a C++ file (basic implementation)."""
        return {}, []

    async def _parse_c_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a C file (basic implementation)."""
        return {}, []

    async def _parse_go_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a Go file (basic implementation)."""
        return {}, []

    async def _parse_rust_file(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Parse a Rust file (basic implementation)."""
        return {}, []

    async def _analyze_dependencies(self, project_path: str, language: str) -> Dict[str, str]:
        """Analyze project dependencies."""
        dependencies = {}

        try:
            if language == "python":
                # Check requirements.txt
                req_file = os.path.join(project_path, "requirements.txt")
                if os.path.exists(req_file):
                    with open(req_file, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                if '==' in line:
                                    name, version = line.split('==', 1)
                                    dependencies[name.strip()] = version.strip()
                                else:
                                    dependencies[line] = "latest"

                # Check pyproject.toml
                pyproject_file = os.path.join(project_path, "pyproject.toml")
                if os.path.exists(pyproject_file):
                    try:
                        import toml
                        with open(pyproject_file, 'r') as f:
                            data = toml.load(f)

                        deps = data.get("tool", {}).get("poetry", {}).get("dependencies", {})
                        for name, version in deps.items():
                            if name != "python":
                                dependencies[name] = str(version)
                    except ImportError:
                        pass

            elif language == "javascript":
                # Check package.json
                package_file = os.path.join(project_path, "package.json")
                if os.path.exists(package_file):
                    with open(package_file, 'r') as f:
                        data = json.load(f)

                    deps = {**data.get("dependencies", {}), **data.get("devDependencies", {})}
                    dependencies.update(deps)

        except Exception as e:
            self.logger.warning(f"Failed to analyze dependencies: {e}")

        return dependencies

    async def _detect_patterns(self, symbols: Dict[str, CodeSymbol], relationships: List[CodeRelationship]) -> List[str]:
        """Detect common patterns in the codebase."""
        patterns = []

        # Count symbol types
        symbol_counts = defaultdict(int)
        for symbol in symbols.values():
            symbol_counts[symbol.type] += 1

        # Detect patterns based on symbol distribution
        if symbol_counts.get("class", 0) > 10:
            patterns.append("object_oriented_design")

        if symbol_counts.get("function", 0) > symbol_counts.get("class", 0) * 3:
            patterns.append("functional_programming")

        # Detect framework patterns
        framework_indicators = {
            "django": ["models.py", "views.py", "urls.py"],
            "flask": ["app.py", "routes.py"],
            "fastapi": ["main.py", "routers"],
            "react": ["components", "hooks"],
            "vue": ["components", ".vue"],
        }

        file_paths = [symbol.file_path for symbol in symbols.values()]
        for framework, indicators in framework_indicators.items():
            if any(indicator in " ".join(file_paths) for indicator in indicators):
                patterns.append(f"{framework}_framework")

        return patterns

    def _serialize_context(self, context: ProjectContext) -> Dict[str, Any]:
        """Serialize project context for API response."""
        return {
            "root_path": context.root_path,
            "language": context.language,
            "framework": context.framework,
            "symbol_count": len(context.symbols),
            "relationship_count": len(context.relationships),
            "file_tree": context.file_tree,
            "dependencies": context.dependencies,
            "patterns": context.patterns,
            "last_updated": context.last_updated.isoformat()
        }

    async def _update_indices(self, context: ProjectContext):
        """Update search indices with new context."""
        # Update symbol index
        for symbol_name, symbol in context.symbols.items():
            self.symbol_index[symbol.name].append(symbol)

        # Update relationship graph
        for relationship in context.relationships:
            self.relationship_graph[relationship.source].append(relationship)

    def _calculate_symbol_relevance(self, query: str, symbol: CodeSymbol) -> float:
        """Calculate relevance score for a symbol given a query."""
        score = 0.0
        query_lower = query.lower()

        # Name match
        if query_lower in symbol.name.lower():
            score += 2.0

        # Definition match
        if symbol.definition and query_lower in symbol.definition.lower():
            score += 1.0

        # Docstring match
        if symbol.docstring and query_lower in symbol.docstring.lower():
            score += 0.5

        # File path match
        if query_lower in symbol.file_path.lower():
            score += 0.3

        return score

    async def _traverse_dependencies(
        self,
        symbol_name: str,
        context: Optional[ProjectContext],
        depth: int,
        visited: Set[str]
    ) -> List[Dict[str, Any]]:
        """Traverse symbol dependencies."""
        if depth <= 0 or symbol_name in visited:
            return []

        visited.add(symbol_name)
        dependencies = []

        if context:
            # Find relationships where this symbol is the target
            for relationship in context.relationships:
                if relationship.target == symbol_name:
                    dep_info = {
                        "symbol": relationship.source,
                        "relationship": relationship.relationship_type,
                        "file_path": relationship.file_path,
                        "line_number": relationship.line_number
                    }
                    dependencies.append(dep_info)

                    # Recursively find dependencies
                    sub_deps = await self._traverse_dependencies(
                        relationship.source, context, depth - 1, visited
                    )
                    dependencies.extend(sub_deps)

        return dependencies

    async def _traverse_dependents(
        self,
        symbol_name: str,
        context: Optional[ProjectContext],
        depth: int,
        visited: Set[str]
    ) -> List[Dict[str, Any]]:
        """Traverse symbol dependents."""
        if depth <= 0 or symbol_name in visited:
            return []

        visited.add(symbol_name)
        dependents = []

        if context:
            # Find relationships where this symbol is the source
            for relationship in context.relationships:
                if relationship.source == symbol_name:
                    dep_info = {
                        "symbol": relationship.target,
                        "relationship": relationship.relationship_type,
                        "file_path": relationship.file_path,
                        "line_number": relationship.line_number
                    }
                    dependents.append(dep_info)

                    # Recursively find dependents
                    sub_deps = await self._traverse_dependents(
                        relationship.target, context, depth - 1, visited
                    )
                    dependents.extend(sub_deps)

        return dependents

    async def _calculate_code_metrics(self, file_path: str, symbols: Dict[str, CodeSymbol]) -> Dict[str, Any]:
        """Calculate code quality metrics."""
        metrics = {
            "lines_of_code": 0,
            "cyclomatic_complexity": 0,
            "function_count": 0,
            "class_count": 0,
            "comment_ratio": 0.0,
            "average_function_length": 0.0
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            metrics["lines_of_code"] = len([line for line in lines if line.strip()])

            # Count symbols
            for symbol in symbols.values():
                if symbol.type == "function":
                    metrics["function_count"] += 1
                elif symbol.type == "class":
                    metrics["class_count"] += 1

            # Calculate comment ratio
            comment_lines = len([line for line in lines if line.strip().startswith('#')])
            if metrics["lines_of_code"] > 0:
                metrics["comment_ratio"] = comment_lines / metrics["lines_of_code"]

            # Calculate average function length (simplified)
            if metrics["function_count"] > 0:
                metrics["average_function_length"] = metrics["lines_of_code"] / metrics["function_count"]

        except Exception as e:
            self.logger.warning(f"Failed to calculate metrics for {file_path}: {e}")

        return metrics

    async def _detect_code_issues(
        self,
        file_path: str,
        symbols: Dict[str, CodeSymbol],
        relationships: List[CodeRelationship]
    ) -> List[Dict[str, Any]]:
        """Detect potential code issues."""
        issues = []

        # Check for long functions
        for symbol in symbols.values():
            if symbol.type == "function" and symbol.definition:
                lines = symbol.definition.count('\n')
                if lines > 50:
                    issues.append({
                        "type": "long_function",
                        "severity": "warning",
                        "message": f"Function '{symbol.name}' is too long ({lines} lines)",
                        "line_number": symbol.line_number,
                        "symbol": symbol.name
                    })

        # Check for missing docstrings
        for symbol in symbols.values():
            if symbol.type in ["function", "class"] and not symbol.docstring:
                issues.append({
                    "type": "missing_docstring",
                    "severity": "info",
                    "message": f"{symbol.type.title()} '{symbol.name}' is missing a docstring",
                    "line_number": symbol.line_number,
                    "symbol": symbol.name
                })

        return issues

    async def _generate_suggestions(
        self,
        file_path: str,
        symbols: Dict[str, CodeSymbol],
        issues: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate improvement suggestions."""
        suggestions = []

        # Suggest refactoring for long functions
        long_functions = [issue for issue in issues if issue["type"] == "long_function"]
        if long_functions:
            suggestions.append({
                "type": "refactoring",
                "priority": "medium",
                "message": f"Consider breaking down {len(long_functions)} long function(s) into smaller, more focused functions",
                "affected_symbols": [issue["symbol"] for issue in long_functions]
            })

        # Suggest adding docstrings
        missing_docs = [issue for issue in issues if issue["type"] == "missing_docstring"]
        if missing_docs:
            suggestions.append({
                "type": "documentation",
                "priority": "low",
                "message": f"Add docstrings to {len(missing_docs)} function(s) and class(es) for better code documentation",
                "affected_symbols": [issue["symbol"] for issue in missing_docs]
            })

        return suggestions

    async def _perform_smart_analysis(
        self,
        project_path: str,
        analysis_type: str,
        include_ai_insights: bool
    ) -> ProjectContext:
        """Perform enhanced smart analysis of the project."""

        # Detect project type and framework
        language, framework = await self._detect_project_type_enhanced(project_path)

        # Build enhanced file tree
        file_tree = await self._build_enhanced_file_tree(project_path)

        # Parse source files with AI enhancement
        symbols = {}
        relationships = []
        insights = []

        source_files = self._get_source_files_filtered(project_path, analysis_type)

        for file_path in source_files:
            try:
                file_symbols, file_relationships = await self._parse_file_enhanced(file_path)
                symbols.update(file_symbols)
                relationships.extend(file_relationships)

                # Generate AI insights if requested
                if include_ai_insights:
                    file_insights = await self._generate_ai_insights(file_path, file_symbols)
                    insights.extend(file_insights)

            except Exception as e:
                self.logger.warning(f"Failed to parse {file_path}: {e}")

        # Analyze dependencies with enhanced detection
        dependencies = await self._analyze_dependencies_enhanced(project_path, language)

        # Detect patterns with AI enhancement
        patterns = await self._detect_patterns_enhanced(symbols, relationships, analysis_type)

        # Calculate quality metrics
        quality_metrics = await self._calculate_project_quality_metrics(symbols, relationships, insights)

        # Generate AI insights for the entire project
        ai_insights = {}
        if include_ai_insights:
            ai_insights = await self._generate_project_ai_insights(
                symbols, relationships, patterns, quality_metrics
            )

        # Create enhanced project context
        context = ProjectContext(
            root_path=project_path,
            language=language,
            framework=framework,
            symbols=symbols,
            relationships=relationships,
            file_tree=file_tree,
            dependencies=dependencies,
            patterns=patterns,
            last_updated=datetime.now(),
            quality_metrics=quality_metrics,
            ai_insights=ai_insights,
            change_history=[]
        )

        # Cache context
        self.project_contexts[project_path] = context

        # Update indices
        await self._update_enhanced_indices(context, insights)

        return context

    async def _detect_project_type_enhanced(self, project_path: str) -> Tuple[str, Optional[str]]:
        """Enhanced project type detection with better framework recognition."""
        files = os.listdir(project_path)

        # Check for specific configuration files
        config_indicators = {
            "package.json": ("javascript", self._detect_js_framework_enhanced),
            "requirements.txt": ("python", self._detect_python_framework_enhanced),
            "pyproject.toml": ("python", self._detect_python_framework_enhanced),
            "setup.py": ("python", self._detect_python_framework_enhanced),
            "Pipfile": ("python", self._detect_python_framework_enhanced),
            "pom.xml": ("java", lambda p: "maven"),
            "build.gradle": ("java", lambda p: "gradle"),
            "Cargo.toml": ("rust", lambda p: "cargo"),
            "go.mod": ("go", lambda p: "modules"),
            "CMakeLists.txt": ("cpp", lambda p: "cmake"),
            "Makefile": ("cpp", lambda p: "make"),
            "composer.json": ("php", self._detect_php_framework_enhanced),
            "*.csproj": ("csharp", lambda p: "dotnet"),
            "Gemfile": ("ruby", self._detect_ruby_framework_enhanced),
        }

        for config_file, (language, framework_detector) in config_indicators.items():
            if config_file in files or any(f.endswith(config_file.replace("*", "")) for f in files):
                framework = framework_detector(project_path) if callable(framework_detector) else framework_detector
                return language, framework

        # Fallback: analyze file extensions with weights
        extension_weights = defaultdict(int)
        for file in files:
            if os.path.isfile(os.path.join(project_path, file)):
                ext = os.path.splitext(file)[1]
                extension_weights[ext] += 1

        # Language mapping with confidence scores
        language_mapping = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".c": "cpp",
            ".go": "go",
            ".rs": "rust",
            ".rb": "ruby",
            ".php": "php",
            ".cs": "csharp",
        }

        if extension_weights:
            most_common_ext = max(extension_weights.items(), key=lambda x: x[1])[0]
            language = language_mapping.get(most_common_ext, "unknown")
            return language, None

        return "unknown", None

    def _detect_js_framework_enhanced(self, project_path: str) -> Optional[str]:
        """Enhanced JavaScript framework detection."""
        try:
            package_json_path = os.path.join(project_path, "package.json")
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)

            dependencies = {**package_data.get("dependencies", {}), **package_data.get("devDependencies", {})}

            # Framework priority order
            framework_indicators = [
                ("next", "nextjs"),
                ("nuxt", "nuxtjs"),
                ("@angular/core", "angular"),
                ("react", "react"),
                ("vue", "vue"),
                ("svelte", "svelte"),
                ("express", "express"),
                ("fastify", "fastify"),
                ("koa", "koa"),
                ("nestjs", "nestjs"),
            ]

            for package, framework in framework_indicators:
                if package in dependencies:
                    return framework

        except Exception:
            pass

        return None

    def _detect_python_framework_enhanced(self, project_path: str) -> Optional[str]:
        """Enhanced Python framework detection."""
        # Check for framework-specific files and directories
        framework_indicators = {
            "manage.py": "django",
            "app.py": "flask",
            "main.py": "fastapi",
            "asgi.py": "fastapi",
            "wsgi.py": "django",
            "requirements.txt": None,  # Will check content
        }

        for indicator, framework in framework_indicators.items():
            file_path = os.path.join(project_path, indicator)
            if os.path.exists(file_path):
                if framework:
                    return framework

                # Check file content for framework imports
                try:
                    with open(file_path, 'r') as f:
                        content = f.read().lower()

                    if "fastapi" in content:
                        return "fastapi"
                    elif "flask" in content:
                        return "flask"
                    elif "django" in content:
                        return "django"
                    elif "tornado" in content:
                        return "tornado"
                    elif "pyramid" in content:
                        return "pyramid"

                except Exception:
                    pass

        # Check directory structure
        if os.path.exists(os.path.join(project_path, "app")):
            return "flask"
        if os.path.exists(os.path.join(project_path, "config", "settings.py")):
            return "django"

        return None

    def _detect_php_framework_enhanced(self, project_path: str) -> Optional[str]:
        """Enhanced PHP framework detection."""
        try:
            composer_path = os.path.join(project_path, "composer.json")
            with open(composer_path, 'r') as f:
                composer_data = json.load(f)

            dependencies = {**composer_data.get("require", {}), **composer_data.get("require-dev", {})}

            framework_indicators = [
                ("laravel/framework", "laravel"),
                ("symfony/symfony", "symfony"),
                ("codeigniter4/framework", "codeigniter"),
                ("cakephp/cakephp", "cakephp"),
                ("yiisoft/yii2", "yii2"),
            ]

            for package, framework in framework_indicators:
                if package in dependencies:
                    return framework

        except Exception:
            pass

        return None

    def _detect_ruby_framework_enhanced(self, project_path: str) -> Optional[str]:
        """Enhanced Ruby framework detection."""
        # Check for Rails
        if os.path.exists(os.path.join(project_path, "config", "application.rb")):
            return "rails"

        # Check Gemfile
        try:
            gemfile_path = os.path.join(project_path, "Gemfile")
            with open(gemfile_path, 'r') as f:
                content = f.read().lower()

            if "rails" in content:
                return "rails"
            elif "sinatra" in content:
                return "sinatra"
            elif "hanami" in content:
                return "hanami"

        except Exception:
            pass

        return None

    def _calculate_quality_score(self, metrics: Dict[str, Any], issues: List[Dict[str, Any]]) -> float:
        """Calculate overall code quality score."""
        score = 100.0

        # Deduct points for issues
        for issue in issues:
            if issue["severity"] == "error":
                score -= 10
            elif issue["severity"] == "warning":
                score -= 5
            elif issue["severity"] == "info":
                score -= 1

        # Adjust based on metrics
        if metrics.get("comment_ratio", 0) < 0.1:
            score -= 5  # Low comment ratio

        if metrics.get("average_function_length", 0) > 30:
            score -= 5  # Functions too long on average

        return max(0.0, min(100.0, score))

    async def _parse_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced file parsing with AI-powered analysis."""
        ext = os.path.splitext(file_path)[1]

        if ext in self.language_parsers:
            return await self.language_parsers[ext](file_path)
        else:
            return {}, []

    async def _parse_python_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced Python file parsing with complexity analysis."""
        symbols = {}
        relationships = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    symbol_name = f"{file_path}::{node.name}"
                    complexity = self._calculate_cyclomatic_complexity(node)
                    quality = self._calculate_function_quality(node, content)

                    symbols[symbol_name] = CodeSymbol(
                        name=node.name,
                        type="function",
                        file_path=file_path,
                        line_number=node.lineno,
                        definition=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node),
                        complexity_score=complexity,
                        quality_score=quality,
                        last_modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
                        semantic_tags=self._extract_semantic_tags(node, content),
                        ai_summary=self._generate_symbol_summary(node, content)
                    )

                elif isinstance(node, ast.ClassDef):
                    symbol_name = f"{file_path}::{node.name}"
                    complexity = self._calculate_class_complexity(node)
                    quality = self._calculate_class_quality(node, content)

                    symbols[symbol_name] = CodeSymbol(
                        name=node.name,
                        type="class",
                        file_path=file_path,
                        line_number=node.lineno,
                        definition=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node),
                        complexity_score=complexity,
                        quality_score=quality,
                        last_modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
                        semantic_tags=self._extract_semantic_tags(node, content),
                        ai_summary=self._generate_symbol_summary(node, content)
                    )

                    # Extract inheritance relationships
                    for base in node.bases:
                        if isinstance(base, ast.Name):
                            relationships.append(CodeRelationship(
                                source=symbol_name,
                                target=base.id,
                                relationship_type="inherits",
                                file_path=file_path,
                                line_number=node.lineno,
                                strength=1.0,
                                context=f"class {node.name} inherits from {base.id}",
                                last_seen=datetime.now()
                            ))

                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    # Handle imports with enhanced tracking
                    self._process_import_node(node, file_path, symbols, relationships)

            # Analyze function calls and relationships
            relationships.extend(self._analyze_function_calls(tree, file_path, content))

        except Exception as e:
            self.logger.warning(f"Failed to parse Python file {file_path}: {e}")

        return symbols, relationships

    def _calculate_cyclomatic_complexity(self, node: ast.AST) -> float:
        """Calculate cyclomatic complexity of a function or class."""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(child, ast.comprehension):
                complexity += 1

        return float(complexity)

    def _calculate_function_quality(self, node: ast.FunctionDef, content: str) -> float:
        """Calculate quality score for a function."""
        score = 10.0  # Base score

        # Check for docstring
        if ast.get_docstring(node):
            score += 2.0
        else:
            score -= 1.0

        # Check function length
        func_lines = len(ast.get_source_segment(content, node).split('\n'))
        if func_lines > 50:
            score -= 2.0
        elif func_lines > 30:
            score -= 1.0

        # Check parameter count
        param_count = len(node.args.args)
        if param_count > 7:
            score -= 2.0
        elif param_count > 5:
            score -= 1.0

        # Check for type hints
        if node.returns or any(arg.annotation for arg in node.args.args):
            score += 1.0

        return max(0.0, min(10.0, score))

    def _calculate_class_quality(self, node: ast.ClassDef, content: str) -> float:
        """Calculate quality score for a class."""
        score = 10.0  # Base score

        # Check for docstring
        if ast.get_docstring(node):
            score += 2.0
        else:
            score -= 1.0

        # Count methods
        methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
        if len(methods) > 20:
            score -= 2.0
        elif len(methods) > 15:
            score -= 1.0

        # Check for proper inheritance
        if node.bases:
            score += 0.5

        return max(0.0, min(10.0, score))

    def _extract_semantic_tags(self, node: ast.AST, content: str) -> List[str]:
        """Extract semantic tags from code node."""
        tags = []

        if isinstance(node, ast.FunctionDef):
            # Function-specific tags
            if node.name.startswith('test_'):
                tags.append('test')
            if node.name.startswith('_'):
                tags.append('private')
            if any(decorator.id == 'property' for decorator in node.decorator_list if isinstance(decorator, ast.Name)):
                tags.append('property')
            if 'async' in ast.dump(node):
                tags.append('async')

        elif isinstance(node, ast.ClassDef):
            # Class-specific tags
            if node.name.endswith('Test'):
                tags.append('test_class')
            if node.name.endswith('Exception'):
                tags.append('exception')
            if any(base.id in ['ABC', 'Protocol'] for base in node.bases if isinstance(base, ast.Name)):
                tags.append('abstract')

        return tags

    def _generate_symbol_summary(self, node: ast.AST, content: str) -> str:
        """Generate AI-powered summary for a code symbol."""
        # Simple summary generation (can be enhanced with actual AI models)
        if isinstance(node, ast.FunctionDef):
            docstring = ast.get_docstring(node)
            if docstring:
                return docstring.split('\n')[0][:100]
            else:
                return f"Function {node.name} with {len(node.args.args)} parameters"

        elif isinstance(node, ast.ClassDef):
            docstring = ast.get_docstring(node)
            if docstring:
                return docstring.split('\n')[0][:100]
            else:
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                return f"Class {node.name} with {len(methods)} methods"

        return "Code symbol"

    async def _generate_ai_insights(self, file_path: str, symbols: Dict[str, CodeSymbol]) -> List[CodeInsight]:
        """Generate AI-powered insights for a file."""
        insights = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check for code smells
            for pattern_name, patterns in self.quality_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    for match in matches:
                        line_number = content[:match.start()].count('\n') + 1

                        insights.append(CodeInsight(
                            type=pattern_name,
                            severity="medium" if pattern_name == "code_smells" else "high",
                            message=self._get_insight_message(pattern_name, match.group()),
                            file_path=file_path,
                            line_number=line_number,
                            suggestion=self._get_insight_suggestion(pattern_name),
                            confidence=0.8,
                            tags=[pattern_name, "automated"]
                        ))

            # Analyze symbol-specific insights
            for symbol in symbols.values():
                if symbol.complexity_score > 10:
                    insights.append(CodeInsight(
                        type="high_complexity",
                        severity="medium",
                        message=f"High complexity detected in {symbol.type} '{symbol.name}' (score: {symbol.complexity_score})",
                        file_path=file_path,
                        line_number=symbol.line_number,
                        suggestion="Consider breaking down into smaller functions",
                        confidence=0.9,
                        tags=["complexity", "refactoring"]
                    ))

                if symbol.quality_score < 5:
                    insights.append(CodeInsight(
                        type="low_quality",
                        severity="low",
                        message=f"Low quality score for {symbol.type} '{symbol.name}' (score: {symbol.quality_score})",
                        file_path=file_path,
                        line_number=symbol.line_number,
                        suggestion="Consider adding documentation and improving structure",
                        confidence=0.7,
                        tags=["quality", "documentation"]
                    ))

        except Exception as e:
            self.logger.warning(f"Failed to generate insights for {file_path}: {e}")

        return insights

    def _get_insight_message(self, pattern_name: str, match_text: str) -> str:
        """Get human-readable message for an insight."""
        messages = {
            "code_smells": "Code smell detected: consider refactoring for better maintainability",
            "security_issues": "Potential security issue: review for safe usage",
            "performance_issues": "Performance issue detected: consider optimization"
        }
        return messages.get(pattern_name, "Code issue detected")

    def _get_insight_suggestion(self, pattern_name: str) -> str:
        """Get suggestion for fixing an insight."""
        suggestions = {
            "code_smells": "Break down large functions/classes into smaller, focused units",
            "security_issues": "Use safer alternatives and validate all inputs",
            "performance_issues": "Optimize algorithms and data structures"
        }
        return suggestions.get(pattern_name, "Review and improve code quality")

    # Enhanced parser methods (placeholders for other languages)
    async def _parse_javascript_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced JavaScript file parsing."""
        # Enhanced version of the existing JavaScript parser
        return await self._parse_javascript_file(file_path)

    async def _parse_typescript_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced TypeScript file parsing."""
        return await self._parse_javascript_file_enhanced(file_path)

    async def _parse_java_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced Java file parsing."""
        return {}, []

    async def _parse_cpp_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced C++ file parsing."""
        return {}, []

    async def _parse_c_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced C file parsing."""
        return {}, []

    async def _parse_go_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced Go file parsing."""
        return {}, []

    async def _parse_rust_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced Rust file parsing."""
        return {}, []

    async def _parse_ruby_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced Ruby file parsing."""
        return {}, []

    async def _parse_php_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced PHP file parsing."""
        return {}, []

    async def _parse_csharp_file_enhanced(self, file_path: str) -> Tuple[Dict[str, CodeSymbol], List[CodeRelationship]]:
        """Enhanced C# file parsing."""
        return {}, []

    def _process_import_node(self, node: ast.AST, file_path: str, symbols: Dict, relationships: List):
        """Process import nodes and create relationships."""
        if isinstance(node, ast.Import):
            for alias in node.names:
                symbol_name = f"{file_path}::import::{alias.name}"
                symbols[symbol_name] = CodeSymbol(
                    name=alias.name,
                    type="import",
                    file_path=file_path,
                    line_number=node.lineno,
                    definition=f"import {alias.name}",
                    last_modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
                    semantic_tags=["import", "external"]
                )

        elif isinstance(node, ast.ImportFrom):
            if node.module:
                for alias in node.names:
                    symbol_name = f"{file_path}::import::{node.module}.{alias.name}"
                    symbols[symbol_name] = CodeSymbol(
                        name=f"{node.module}.{alias.name}",
                        type="import",
                        file_path=file_path,
                        line_number=node.lineno,
                        definition=f"from {node.module} import {alias.name}",
                        last_modified=datetime.fromtimestamp(os.path.getmtime(file_path)),
                        semantic_tags=["import", "external"]
                    )

    def _analyze_function_calls(self, tree: ast.AST, file_path: str, content: str) -> List[CodeRelationship]:
        """Analyze function calls and create relationships."""
        relationships = []

        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    # Simple function call
                    relationships.append(CodeRelationship(
                        source=f"{file_path}::caller",
                        target=node.func.id,
                        relationship_type="calls",
                        file_path=file_path,
                        line_number=node.lineno,
                        strength=1.0,
                        context=f"Function call to {node.func.id}",
                        last_seen=datetime.now()
                    ))
                elif isinstance(node.func, ast.Attribute):
                    # Method call
                    if isinstance(node.func.value, ast.Name):
                        relationships.append(CodeRelationship(
                            source=f"{file_path}::caller",
                            target=f"{node.func.value.id}.{node.func.attr}",
                            relationship_type="calls",
                            file_path=file_path,
                            line_number=node.lineno,
                            strength=1.0,
                            context=f"Method call to {node.func.value.id}.{node.func.attr}",
                            last_seen=datetime.now()
                        ))

        return relationships

    def _get_source_files_filtered(self, project_path: str, analysis_type: str) -> List[str]:
        """Get filtered list of source files based on analysis type."""
        all_files = self._get_source_files(project_path)

        if analysis_type == "quick":
            # For quick analysis, limit to main files
            return [f for f in all_files if not any(pattern in f for pattern in ['test', 'spec', '__pycache__'])][:50]
        elif analysis_type == "security":
            # Focus on files that might have security implications
            return [f for f in all_files if any(keyword in f.lower() for keyword in ['auth', 'login', 'password', 'token', 'security', 'crypto'])]
        elif analysis_type == "performance":
            # Focus on files that might have performance implications
            return [f for f in all_files if any(keyword in f.lower() for keyword in ['api', 'database', 'db', 'query', 'cache', 'process'])]
        else:
            # Comprehensive analysis
            return all_files

    async def _build_enhanced_file_tree(self, project_path: str) -> Dict[str, Any]:
        """Build enhanced file tree with metadata."""
        def build_tree_with_metadata(path: str) -> Dict[str, Any]:
            tree = {"type": "directory", "children": {}, "metadata": {}}

            try:
                items = os.listdir(path)
                tree["metadata"]["item_count"] = len(items)

                for item in items:
                    if item.startswith('.') or item in self.ignore_patterns:
                        continue

                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        tree["children"][item] = build_tree_with_metadata(item_path)
                    else:
                        stat = os.stat(item_path)
                        tree["children"][item] = {
                            "type": "file",
                            "size": stat.st_size,
                            "extension": os.path.splitext(item)[1],
                            "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "metadata": {
                                "is_source": os.path.splitext(item)[1] in self.language_parsers,
                                "is_config": item in ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod'],
                                "is_documentation": item.lower() in ['readme.md', 'readme.txt', 'changelog.md']
                            }
                        }
            except PermissionError:
                tree["metadata"]["error"] = "Permission denied"

            return tree

        return build_tree_with_metadata(project_path)

    async def _serialize_enhanced_context(self, context: ProjectContext) -> Dict[str, Any]:
        """Serialize enhanced project context for API response."""
        return {
            "root_path": context.root_path,
            "language": context.language,
            "framework": context.framework,
            "symbol_count": len(context.symbols),
            "relationship_count": len(context.relationships),
            "file_tree": context.file_tree,
            "dependencies": context.dependencies,
            "patterns": context.patterns,
            "quality_metrics": context.quality_metrics or {},
            "ai_insights": context.ai_insights or {},
            "last_updated": context.last_updated.isoformat(),
            "symbols_summary": {
                "by_type": Counter(symbol.type for symbol in context.symbols.values()),
                "avg_complexity": sum(symbol.complexity_score for symbol in context.symbols.values()) / len(context.symbols) if context.symbols else 0,
                "avg_quality": sum(symbol.quality_score for symbol in context.symbols.values()) / len(context.symbols) if context.symbols else 0
            }
        }

    async def execute(self, **kwargs) -> ToolResult:
        """Execute context engine operations based on the action parameter.

        Args:
            **kwargs: Operation parameters including:
                - action: The operation to perform ('analyze', 'quality', 'security')
                - project_path: Path to analyze
                - analysis_type: Type of analysis ('quick', 'comprehensive')
                - include_ai_insights: Whether to include AI insights
                - Other action-specific parameters

        Returns:
            ToolResult with operation results
        """
        try:
            action = kwargs.get('action', 'analyze')
            project_path = kwargs.get('project_path', '.')

            if action == 'analyze':
                analysis_type = kwargs.get('analysis_type', 'quick')
                include_ai_insights = kwargs.get('include_ai_insights', True)

                return await self.smart_analyze(
                    project_path=project_path,
                    analysis_type=analysis_type,
                    include_ai_insights=include_ai_insights
                )

            elif action == 'quality':
                focus = kwargs.get('focus', 'general')

                # For now, return a placeholder result
                # In a full implementation, this would perform quality analysis
                return ToolResult(
                    success=True,
                    data={
                        "message": f"Quality analysis for {project_path}",
                        "focus": focus,
                        "quality_score": 0.8
                    }
                )

            elif action == 'security':
                level = kwargs.get('level', 'basic')

                # For now, return a placeholder result
                # In a full implementation, this would perform security analysis
                return ToolResult(
                    success=True,
                    data={
                        "message": f"Security analysis for {project_path}",
                        "level": level,
                        "security_score": 0.9
                    }
                )

            else:
                return ToolResult(
                    success=False,
                    error=f"Unknown action: {action}"
                )

        except Exception as e:
            self.logger.error(f"Context engine execution failed: {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
