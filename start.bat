@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Reverie CLI - Main Launcher Script
:: =============================================================================

title Reverie CLI - AI-Native Development Server

echo.
echo ===============================================================================
echo                            Reverie CLI v0.1.0 
echo                      AI-Native Development Server
echo ===============================================================================
echo.

:: Configuration
set "VENV_NAME=reverie_env"
set "DEFAULT_HOST=127.0.0.1"
set "DEFAULT_PORT=8000"
set "PROJECT_DIR=%~dp0"

:: Parse command line arguments
set "MODE="
set "HOST=%DEFAULT_HOST%"
set "PORT=%DEFAULT_PORT%"

:PARSE_ARGS
if "%~1"=="" goto CHECK_ENV
if "%~1"=="--server" set "MODE=server" & shift & goto PARSE_ARGS
if "%~1"=="--console" set "MODE=console" & shift & goto PARSE_ARGS
if "%~1"=="--dual" set "MODE=dual" & shift & goto PARSE_ARGS
if "%~1"=="--host" set "HOST=%~2" & shift & shift & goto PARSE_ARGS
if "%~1"=="--port" set "PORT=%~2" & shift & shift & goto PARSE_ARGS
if "%~1"=="--help" goto SHOW_HELP
shift
goto PARSE_ARGS

:CHECK_ENV
:: Check virtual environment
echo [INFO] Checking environment...
if not exist "%VENV_NAME%" (
    echo [ERROR] Virtual environment not found: %VENV_NAME%
    echo [INFO] Please run 'setup.bat' first to create the environment
    pause
    exit /b 1
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call "%VENV_NAME%\Scripts\activate.bat"
if errorlevel 1 (
    echo [ERROR] Failed to activate virtual environment
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment activated

:: Check Python and dependencies
echo [INFO] Checking dependencies...
python -c "import reverie_cli" 2>nul
if errorlevel 1 (
    echo [WARNING] Reverie CLI module not properly installed
    echo [INFO] Attempting to reinstall...
    python -m pip install -e . >nul 2>&1
)

:: Set environment variables
set "PYTHONPATH=%PROJECT_DIR%;%PYTHONPATH%"

:: Determine mode if not specified
if "%MODE%"=="" (
    goto INTERACTIVE_MODE
) else (
    goto START_MODE
)

:INTERACTIVE_MODE
echo.
echo ===============================================================================
echo                              Mode Selection
echo ===============================================================================
echo.
echo Available modes:
echo   1. Server Mode    - Web interface + API service
echo   2. Console Mode   - Interactive dual-mode console
echo   3. Dual Mode      - Server + Console simultaneously
echo   4. Help           - Show detailed help and commands
echo   5. Exit           - Exit application
echo.

set /p "CHOICE=Select mode (1-5): "

if "%CHOICE%"=="1" set "MODE=server" & goto START_MODE
if "%CHOICE%"=="2" set "MODE=console" & goto START_MODE
if "%CHOICE%"=="3" set "MODE=dual" & goto START_MODE
if "%CHOICE%"=="4" goto SHOW_HELP
if "%CHOICE%"=="5" goto EXIT
echo [ERROR] Invalid choice. Please select 1-5.
goto INTERACTIVE_MODE

:START_MODE
if "%MODE%"=="server" goto START_SERVER
if "%MODE%"=="console" goto START_CONSOLE
if "%MODE%"=="dual" goto START_DUAL
goto INTERACTIVE_MODE

:START_SERVER
echo.
echo ===============================================================================
echo                            Starting Server Mode
echo ===============================================================================
echo.

:: Check port availability
netstat -an | findstr ":%PORT% " >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port %PORT% is already in use
    set /p "NEW_PORT=Enter new port (default: 8001): "
    if "!NEW_PORT!"=="" set "NEW_PORT=8001"
    set "PORT=!NEW_PORT!"
)

echo [INFO] Server Configuration:
echo   - Address: http://%HOST%:%PORT%
echo   - Project: %PROJECT_DIR%
echo   - Mode: Dual-Mode (AI Coder + API Service)
echo.

echo [INFO] Starting server... (Press Ctrl+C to stop)
echo.

:: Set PYTHONPATH to include current directory
set "PYTHONPATH=%PROJECT_DIR%;%PYTHONPATH%"

python -m reverie_cli.main start --host %HOST% --port %PORT%
goto END

:START_CONSOLE
echo.
echo ===============================================================================
echo                           Starting Console Mode
echo ===============================================================================
echo.
echo [INFO] Dual-Mode Console Features:
echo   - AI Coder Mode: Natural language programming assistant
echo   - API Service Mode: Server management and monitoring
echo   - Enhanced Engines: Web, Context, Memory integration
echo   - One-Line Operations: Complex tasks in single commands
echo.
echo [INFO] Starting console... (Type 'help' for commands, 'exit' to quit)
echo.

python -c "
import asyncio
import sys
from reverie_cli.core.console import DualModeConsole

async def main():
    try:
        console = DualModeConsole()
        await console.run_dual_mode()
    except KeyboardInterrupt:
        print('\n[INFO] Console stopped by user')
    except Exception as e:
        print(f'\n[ERROR] Console error: {e}')
        print('[INFO] Please check installation and configuration')
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
"
goto END

:START_DUAL
echo.
echo ===============================================================================
echo                            Starting Dual Mode
echo ===============================================================================
echo.
echo [INFO] Starting server in background...
start "Reverie CLI Server" cmd /c "call reverie_env\Scripts\activate.bat & set PYTHONPATH=%PROJECT_DIR%;%%PYTHONPATH%% & python -m reverie_cli.main start --host %HOST% --port %PORT%"

echo [INFO] Waiting for server to start...
timeout /t 3 /nobreak >nul

echo [INFO] Starting console in foreground...
call start.bat --console
goto END

:SHOW_HELP
echo.
echo ===============================================================================
echo                              Reverie CLI Help
echo ===============================================================================
echo.
echo USAGE:
echo   start.bat [OPTIONS]
echo.
echo OPTIONS:
echo   --server              Start in server mode only
echo   --console             Start in console mode only
echo   --dual                Start both server and console
echo   --host HOST           Server host address (default: 127.0.0.1)
echo   --port PORT           Server port (default: 8000)
echo   --help                Show this help message
echo.
echo EXAMPLES:
echo   start.bat                             # Interactive mode selection
echo   start.bat --server                    # Start server only
echo   start.bat --console                   # Start console only
echo   start.bat --dual                      # Start both server and console
echo   start.bat --server --port 8080        # Start server on port 8080
echo.
echo CONSOLE COMMANDS:
echo.
echo AI Coder Mode Commands:
echo   ai "request"                          # Direct AI assistance
echo   code create file.py "description"     # Create code file
echo   code edit file.py "changes"           # Edit existing file
echo   code analyze path                     # Analyze code
echo   code review path                      # Review code quality
echo   code test path                        # Generate/run tests
echo.
echo Enhanced Engine Commands:
echo   web "search query"                    # Smart web search
echo   web extract url                       # Extract content from URL
echo   web monitor url                       # Monitor website changes
echo   search "query"                        # Search memory/knowledge
echo   remember "content" [tags]             # Store in memory
echo   context analyze path                  # Analyze project context
echo   context set path                      # Set project context
echo.
echo System Commands:
echo   status                                # Show system status
echo   models                                # List available models
echo   models load model_name                # Load specific model
echo   config show                           # Show configuration
echo   config set key value                  # Set configuration
echo   help                                  # Show help
echo   clear                                 # Clear screen
echo   exit                                  # Exit console
echo.
echo One-Line Complex Operations:
echo   smart_solution "problem"              # AI + Web + Memory solution
echo   project_health "path"                 # Complete project analysis
echo   full_analysis "path"                  # Code + Quality + Security
echo.
echo ACCESS URLS (when server is running):
echo   http://localhost:8000/                # Main web interface
echo   http://localhost:8000/docs            # API documentation
echo   http://localhost:8000/chat            # Chat interface
echo   http://localhost:8000/health          # System health
echo.
echo For more information, visit: docs/ENHANCED_FEATURES.md
echo.
pause
goto INTERACTIVE_MODE

:EXIT
echo.
echo [INFO] Thank you for using Reverie CLI!
goto END

:END
echo.
if errorlevel 1 (
    echo [ERROR] Application exited with error
    echo [INFO] Check logs for details: logs/reverie_cli.log
    pause
)
pause
