"""
Main entry point for Reverie CLI.

This module provides the main entry point that combines both server and CLI modes,
allowing users to run the FastAPI server while also having access to an interactive
console for direct AI coding assistance.
"""

import asyncio
import sys
import threading
import socket
from pathlib import Path
from typing import Optional

import typer
import uvicorn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from reverie_cli import __version__, get_logger, get_settings
from reverie_cli.core.logging import setup_logging, log_startup
from reverie_cli.core.console import InteractiveConsole
from reverie_cli.api.server import create_app


# Initialize components
console = Console()
app = typer.Typer(
    name="reverie",
    help="AI-native code development server with Augment-like capabilities",
    add_completion=False,
)


def is_port_available(host: str, port: int) -> bool:
    """Check if a port is available."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            return result != 0
    except Exception:
        return False


def find_available_port(host: str, start_port: int, max_attempts: int = 10) -> int:
    """Find an available port starting from start_port."""
    for port in range(start_port, start_port + max_attempts):
        if is_port_available(host, port):
            return port
    raise RuntimeError(f"No available port found in range {start_port}-{start_port + max_attempts}")


def print_banner():
    """Print the Reverie CLI banner."""
    banner_text = Text()
    banner_text.append("🌟 ", style="bright_yellow")
    banner_text.append("Reverie CLI", style="bold bright_blue")
    banner_text.append(f" v{__version__}", style="dim")
    banner_text.append("\n")
    banner_text.append("AI-native code development server", style="italic")

    panel = Panel(
        banner_text,
        title="Welcome",
        border_style="bright_blue",
        padding=(1, 2),
    )
    console.print(panel)


def print_startup_info(host: str, port: int, interactive: bool):
    """Print startup information."""
    info_text = Text()
    info_text.append("🚀 Server starting at: ", style="green")
    info_text.append(f"http://{host}:{port}", style="bold blue underline")
    info_text.append("\n")
    info_text.append("📚 API Documentation: ", style="green")
    info_text.append(f"http://{host}:{port}/docs", style="bold blue underline")
    info_text.append("\n")
    info_text.append("💬 Web Interface: ", style="green")
    info_text.append(f"http://{host}:{port}", style="bold blue underline")
    
    if interactive:
        info_text.append("\n\n")
        info_text.append("🤖 Interactive console enabled", style="bright_green")
        info_text.append("\n")
        info_text.append("Type 'help' for available commands", style="dim")
    
    panel = Panel(
        info_text,
        title="Server Information",
        border_style="green",
        padding=(1, 2),
    )
    console.print(panel)


def get_available_port(host: str, port: int) -> int:
    """Get an available port, starting from the requested port."""
    if is_port_available(host, port):
        return port

    console.print(f"⚠️  Port {port} is already in use", style="yellow")
    try:
        available_port = find_available_port(host, port + 1)
        console.print(f"🔄 Using available port {available_port} instead", style="green")
        return available_port
    except RuntimeError as e:
        console.print(f"❌ {e}", style="red")
        sys.exit(1)


def run_server(host: str, port: int, reload: bool = False, workers: int = 1):
    """Run the FastAPI server."""
    try:
        # Create FastAPI app
        fastapi_app = create_app()

        # Configure uvicorn
        config = uvicorn.Config(
            app=fastapi_app,
            host=host,
            port=port,
            reload=reload,
            workers=workers if not reload else 1,
            log_config=None,  # We handle logging ourselves
            access_log=False,  # Disable uvicorn access logs
        )

        server = uvicorn.Server(config)
        server.run()

    except Exception as e:
        logger = get_logger("server")
        logger.error(f"Failed to start server: {e}")
        console.print(f"❌ Failed to start server: {e}", style="red")
        sys.exit(1)


def run_interactive_console():
    """Run the interactive console."""
    try:
        console_instance = InteractiveConsole()
        console_instance.run()
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="yellow")
    except Exception as e:
        logger = get_logger("console")
        logger.error(f"Console error: {e}")
        console.print(f"❌ Console error: {e}", style="red")


@app.command()
def start(
    host: str = typer.Option("127.0.0.1", help="Server host"),
    port: int = typer.Option(8000, help="Server port"),
    reload: bool = typer.Option(False, help="Enable auto-reload"),
    workers: int = typer.Option(1, help="Number of workers"),
    no_interactive: bool = typer.Option(False, help="Disable interactive console"),
    config_file: Optional[str] = typer.Option(None, help="Configuration file path"),
):
    """Start the Reverie CLI server with optional interactive console."""
    
    # Setup logging
    setup_logging()
    logger = get_logger("main")
    
    # Print banner
    print_banner()
    
    # Load settings
    settings = get_settings()
    if config_file:
        # TODO: Load custom config file
        pass
    
    # Check and get available port
    actual_port = get_available_port(host, port)

    # Override settings with CLI arguments
    settings.server.host = host
    settings.server.port = actual_port
    settings.server.reload = reload
    settings.server.workers = workers

    # Log startup
    log_startup("Reverie CLI", __version__, host=host, port=actual_port)

    # Print startup info
    print_startup_info(host, actual_port, not no_interactive)

    if no_interactive:
        # Run only the server
        run_server(host, actual_port, reload, workers)
    else:
        # Run server in a separate thread
        server_thread = threading.Thread(
            target=run_server,
            args=(host, actual_port, reload, workers),
            daemon=True
        )
        server_thread.start()
        
        # Give server time to start
        import time
        time.sleep(2)
        
        # Run interactive console in main thread
        run_interactive_console()


@app.command()
def version():
    """Show version information."""
    console.print(f"Reverie CLI v{__version__}", style="bold blue")


@app.command()
def config():
    """Show current configuration."""
    settings = get_settings()
    
    config_text = Text()
    config_text.append("📋 Current Configuration\n\n", style="bold")
    
    # Server config
    config_text.append("🖥️  Server:\n", style="bold green")
    config_text.append(f"   Host: {settings.server.host}\n")
    config_text.append(f"   Port: {settings.server.port}\n")
    config_text.append(f"   Workers: {settings.server.workers}\n\n")
    
    # Model config
    config_text.append("🤖 Model:\n", style="bold blue")
    config_text.append(f"   Default: {settings.model.default_model}\n")
    config_text.append(f"   Context Length: {settings.model.max_context_length:,}\n")
    config_text.append(f"   Temperature: {settings.model.temperature}\n\n")
    
    # Agent config
    config_text.append("🧠 Agent:\n", style="bold purple")
    config_text.append(f"   Max Iterations: {settings.agent.max_iterations}\n")
    config_text.append(f"   Memory Enabled: {settings.agent.memory_enabled}\n")
    config_text.append(f"   Code Analysis: {settings.agent.enable_code_analysis}\n")
    
    panel = Panel(
        config_text,
        title="Configuration",
        border_style="blue",
        padding=(1, 2),
    )
    console.print(panel)


def main():
    """Main entry point."""
    try:
        # If no arguments provided, run start command by default
        if len(sys.argv) == 1:
            start()
        else:
            app()
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="yellow")
        sys.exit(0)
    except Exception as e:
        console.print(f"❌ Unexpected error: {e}", style="red")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
