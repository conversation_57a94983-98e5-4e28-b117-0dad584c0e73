"""
Tool manager for Reverie CLI.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ToolError, ToolNotFoundError, ToolExecutionError
from reverie_cli.tools.base import <PERSON><PERSON>ool, ToolResult, ToolInfo
from reverie_cli.tools.registry import (
    get_tool, list_tools, tool_exists, get_tools_by_category, get_registry_stats
)


class ToolManager:
    """
    Central tool management system.
    
    Manages tool execution, monitoring, and coordination for AI agents.
    """
    
    def __init__(self):
        self.logger = get_logger("tool_manager")
        
        # Execution tracking
        self.execution_count: Dict[str, int] = {}
        self.execution_times: Dict[str, List[float]] = {}
        self.last_execution: Dict[str, datetime] = {}
        
        # Tool state
        self.enabled_tools: set = set()
        self.disabled_tools: set = set()
        
        self.logger.info("ToolManager initialized")
    
    async def initialize(self):
        """Initialize the tool manager."""
        self.logger.info("Initializing ToolManager")
        
        # Enable all tools by default
        tools = list_tools()
        for tool_info in tools:
            self.enabled_tools.add(tool_info.name)
        
        self.logger.info(f"Enabled {len(self.enabled_tools)} tools")
    
    def list_available_tools(self) -> List[ToolInfo]:
        """List all available tools."""
        return list_tools()
    
    def get_tool_info(self, tool_name: str) -> Optional[ToolInfo]:
        """Get information about a specific tool."""
        tool = get_tool(tool_name)
        return tool.get_info() if tool else None
    
    def is_tool_enabled(self, tool_name: str) -> bool:
        """Check if a tool is enabled."""
        return tool_name in self.enabled_tools
    
    def enable_tool(self, tool_name: str) -> bool:
        """Enable a tool."""
        if not tool_exists(tool_name):
            return False
        
        self.enabled_tools.add(tool_name)
        self.disabled_tools.discard(tool_name)
        self.logger.info(f"Enabled tool: {tool_name}")
        return True
    
    def disable_tool(self, tool_name: str) -> bool:
        """Disable a tool."""
        if not tool_exists(tool_name):
            return False
        
        self.disabled_tools.add(tool_name)
        self.enabled_tools.discard(tool_name)
        self.logger.info(f"Disabled tool: {tool_name}")
        return True
    
    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> ToolResult:
        """
        Execute a tool with given parameters.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            timeout: Execution timeout in seconds
            
        Returns:
            Tool execution result
            
        Raises:
            ToolNotFoundError: If tool is not found
            ToolExecutionError: If tool execution fails
        """
        # Check if tool exists
        if not tool_exists(tool_name):
            raise ToolNotFoundError(f"Tool not found: {tool_name}")
        
        # Check if tool is enabled
        if not self.is_tool_enabled(tool_name):
            raise ToolExecutionError(f"Tool is disabled: {tool_name}")
        
        # Get tool instance
        tool = get_tool(tool_name)
        if not tool:
            raise ToolNotFoundError(f"Tool not found: {tool_name}")
        
        self.logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")
        
        try:
            # Execute tool with optional timeout
            if timeout:
                result = await asyncio.wait_for(
                    tool.safe_execute(parameters),
                    timeout=timeout
                )
            else:
                result = await tool.safe_execute(parameters)
            
            # Track execution
            self._track_execution(tool_name, result.execution_time)
            
            self.logger.info(
                f"Tool {tool_name} executed: success={result.success}, "
                f"time={result.execution_time:.3f}s"
            )
            
            return result
            
        except asyncio.TimeoutError:
            error_msg = f"Tool {tool_name} execution timed out after {timeout}s"
            self.logger.error(error_msg)
            return ToolResult(
                success=False,
                error="Execution timeout",
                message=error_msg
            )
        except Exception as e:
            error_msg = f"Tool {tool_name} execution failed: {e}"
            self.logger.error(error_msg)
            raise ToolExecutionError(error_msg)
    
    async def execute_multiple_tools(
        self,
        tool_executions: List[Dict[str, Any]],
        parallel: bool = False
    ) -> List[ToolResult]:
        """
        Execute multiple tools.
        
        Args:
            tool_executions: List of tool execution configs
            parallel: Whether to execute in parallel
            
        Returns:
            List of tool execution results
        """
        if parallel:
            # Execute in parallel
            tasks = []
            for execution in tool_executions:
                task = self.execute_tool(
                    execution["tool_name"],
                    execution["parameters"],
                    execution.get("timeout")
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Convert exceptions to error results
            final_results = []
            for result in results:
                if isinstance(result, Exception):
                    final_results.append(ToolResult(
                        success=False,
                        error=str(result),
                        message="Tool execution failed"
                    ))
                else:
                    final_results.append(result)
            
            return final_results
        else:
            # Execute sequentially
            results = []
            for execution in tool_executions:
                result = await self.execute_tool(
                    execution["tool_name"],
                    execution["parameters"],
                    execution.get("timeout")
                )
                results.append(result)
            
            return results
    
    def get_tools_by_category(self, category: str) -> List[ToolInfo]:
        """Get tools by category."""
        tools = get_tools_by_category(category)
        return [tool.get_info() for tool in tools if self.is_tool_enabled(tool.name)]
    
    def search_tools(self, query: str) -> List[ToolInfo]:
        """Search tools by name or description."""
        query = query.lower()
        results = []
        
        for tool_info in self.list_available_tools():
            if not self.is_tool_enabled(tool_info.name):
                continue
            
            # Search in name
            if query in tool_info.name.lower():
                results.append(tool_info)
                continue
            
            # Search in description
            if query in tool_info.description.lower():
                results.append(tool_info)
                continue
        
        return results
    
    def _track_execution(self, tool_name: str, execution_time: float):
        """Track tool execution statistics."""
        # Update execution count
        self.execution_count[tool_name] = self.execution_count.get(tool_name, 0) + 1
        
        # Track execution times
        if tool_name not in self.execution_times:
            self.execution_times[tool_name] = []
        self.execution_times[tool_name].append(execution_time)
        
        # Keep only last 100 execution times
        if len(self.execution_times[tool_name]) > 100:
            self.execution_times[tool_name] = self.execution_times[tool_name][-100:]
        
        # Update last execution time
        self.last_execution[tool_name] = datetime.now()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get tool performance statistics."""
        stats = {
            "total_tools": len(self.list_available_tools()),
            "enabled_tools": len(self.enabled_tools),
            "disabled_tools": len(self.disabled_tools),
            "execution_count": self.execution_count.copy(),
            "registry_stats": get_registry_stats()
        }
        
        # Calculate average execution times
        avg_times = {}
        for tool_name, times in self.execution_times.items():
            if times:
                avg_times[tool_name] = sum(times) / len(times)
        stats["average_execution_times"] = avg_times
        
        return stats
    
    def get_tool_usage_summary(self) -> Dict[str, Any]:
        """Get tool usage summary."""
        total_executions = sum(self.execution_count.values())
        
        return {
            "total_executions": total_executions,
            "most_used_tools": sorted(
                self.execution_count.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10],
            "recently_used": [
                (tool_name, last_time.isoformat())
                for tool_name, last_time in sorted(
                    self.last_execution.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]
            ]
        }


# Global tool manager instance
_tool_manager: Optional[ToolManager] = None


def get_tool_manager() -> ToolManager:
    """Get the global tool manager instance."""
    global _tool_manager
    if _tool_manager is None:
        _tool_manager = ToolManager()
    return _tool_manager
