"""
File operations endpoints.
"""

from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger


router = APIRouter()
logger = get_logger("files")


class FileInfo(BaseModel):
    """File information model."""
    name: str
    path: str
    size: int
    is_directory: bool
    modified_at: str


class CreateFileRequest(BaseModel):
    """Create file request."""
    path: str = Field(..., description="File path")
    content: str = Field("", description="File content")
    overwrite: bool = Field(False, description="Overwrite if exists")


class ReadFileResponse(BaseModel):
    """Read file response."""
    path: str
    content: str
    size: int
    encoding: str


@router.get("/files", response_model=List[FileInfo])
async def list_files(directory: str = "."):
    """List files in a directory."""
    try:
        path = Path(directory)
        if not path.exists():
            raise HTTPException(status_code=404, detail="Directory not found")
        
        files = []
        for item in path.iterdir():
            files.append(FileInfo(
                name=item.name,
                path=str(item),
                size=item.stat().st_size if item.is_file() else 0,
                is_directory=item.is_dir(),
                modified_at=str(item.stat().st_mtime)
            ))
        
        return files
        
    except Exception as e:
        logger.error(f"Failed to list files: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list files: {e}")


@router.post("/files/create")
async def create_file(request: CreateFileRequest):
    """Create a new file."""
    try:
        path = Path(request.path)
        
        if path.exists() and not request.overwrite:
            raise HTTPException(status_code=409, detail="File already exists")
        
        # Create parent directories if needed
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write file content
        path.write_text(request.content, encoding="utf-8")
        
        return {"success": True, "message": f"File created: {request.path}"}
        
    except Exception as e:
        logger.error(f"Failed to create file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create file: {e}")


@router.get("/files/read", response_model=ReadFileResponse)
async def read_file(file_path: str):
    """Read file content."""
    try:
        path = Path(file_path)
        
        if not path.exists():
            raise HTTPException(status_code=404, detail="File not found")
        
        if not path.is_file():
            raise HTTPException(status_code=400, detail="Path is not a file")
        
        content = path.read_text(encoding="utf-8")
        
        return ReadFileResponse(
            path=file_path,
            content=content,
            size=path.stat().st_size,
            encoding="utf-8"
        )
        
    except Exception as e:
        logger.error(f"Failed to read file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read file: {e}")


@router.delete("/files/delete")
async def delete_file(file_path: str):
    """Delete a file."""
    try:
        path = Path(file_path)
        
        if not path.exists():
            raise HTTPException(status_code=404, detail="File not found")
        
        if path.is_file():
            path.unlink()
        elif path.is_dir():
            import shutil
            shutil.rmtree(path)
        
        return {"success": True, "message": f"File deleted: {file_path}"}
        
    except Exception as e:
        logger.error(f"Failed to delete file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {e}")
