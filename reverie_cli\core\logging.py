"""
Enhanced logging system for Reverie CLI.

This module provides a rich, configurable logging system using Loguru
with support for console and file output, structured logging, and
performance monitoring.
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any
from functools import lru_cache

from loguru import logger
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from reverie_cli.core.config import get_settings


class ReverieLogger:
    """Enhanced logger with Rich integration and performance monitoring."""
    
    def __init__(self):
        self.settings = get_settings()
        self.console = Console()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with console and file handlers."""
        # Remove default handler
        logger.remove()
        
        # Console handler with Rich
        if self.settings.logging.console_enabled:
            logger.add(
                sys.stderr,
                format=self.settings.logging.format,
                level=self.settings.logging.level,
                colorize=True,
                backtrace=True,
                diagnose=True,
            )
        
        # File handler
        if self.settings.logging.file_enabled:
            log_path = Path(self.settings.logging.file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            logger.add(
                log_path,
                format=self.settings.logging.format,
                level=self.settings.logging.level,
                rotation=self.settings.logging.file_rotation,
                retention=self.settings.logging.file_retention,
                compression="zip",
                backtrace=True,
                diagnose=True,
            )
    
    def get_logger(self, name: str):
        """Get a logger instance for a specific module."""
        return logger.bind(name=name)
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log performance metrics."""
        logger.info(
            f"Performance: {operation} completed in {duration:.3f}s",
            extra={"operation": operation, "duration": duration, **kwargs}
        )
    
    def log_model_info(self, model_name: str, backend: str, device: str, **kwargs):
        """Log model loading information."""
        logger.info(
            f"Model loaded: {model_name} on {backend} ({device})",
            extra={"model": model_name, "backend": backend, "device": device, **kwargs}
        )
    
    def log_agent_action(self, action: str, tool: str, result: str, **kwargs):
        """Log agent actions."""
        logger.info(
            f"Agent action: {action} using {tool} -> {result}",
            extra={"action": action, "tool": tool, "result": result, **kwargs}
        )
    
    def log_api_request(self, method: str, path: str, status: int, duration: float):
        """Log API requests."""
        logger.info(
            f"API {method} {path} -> {status} ({duration:.3f}s)",
            extra={"method": method, "path": path, "status": status, "duration": duration}
        )


# Global logger instance
_logger_instance: Optional[ReverieLogger] = None


def setup_logging() -> ReverieLogger:
    """Setup and return the global logger instance."""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = ReverieLogger()
    return _logger_instance


@lru_cache()
def get_logger(name: str = "reverie_cli"):
    """Get a cached logger instance for a specific module."""
    if _logger_instance is None:
        setup_logging()
    return _logger_instance.get_logger(name)


# Convenience functions for common logging patterns
def log_startup(component: str, version: str, **kwargs):
    """Log component startup."""
    logger = get_logger("startup")
    logger.info(f"{component} v{version} starting up", extra=kwargs)


def log_shutdown(component: str, **kwargs):
    """Log component shutdown."""
    logger = get_logger("shutdown")
    logger.info(f"{component} shutting down", extra=kwargs)


def log_error(error: Exception, context: str = "", **kwargs):
    """Log errors with context."""
    logger = get_logger("error")
    logger.error(f"Error in {context}: {error}", extra={"error_type": type(error).__name__, **kwargs})


def log_warning(message: str, **kwargs):
    """Log warnings."""
    logger = get_logger("warning")
    logger.warning(message, extra=kwargs)


def log_debug(message: str, **kwargs):
    """Log debug messages."""
    logger = get_logger("debug")
    logger.debug(message, extra=kwargs)


# Performance monitoring decorator
def log_performance_decorator(operation_name: str):
    """Decorator to log function performance."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                if _logger_instance:
                    _logger_instance.log_performance(operation_name, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                log_error(e, f"{operation_name} (failed after {duration:.3f}s)")
                raise
        return wrapper
    return decorator


def get_logger(name: str = "reverie_cli"):
    """Get a logger instance for the specified module.

    Args:
        name: Logger name, typically the module name

    Returns:
        Logger instance
    """
    return logger.bind(module=name)
