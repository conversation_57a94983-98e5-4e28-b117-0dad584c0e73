"""
Core module for Reverie CLI.

This module contains the fundamental components that power the entire system:
- Configuration management
- Logging infrastructure  
- Exception handling
- Console interface
- Base utilities

All other modules depend on these core components.
"""

from reverie_cli.core.config import Settings, get_settings
from reverie_cli.core.logging import setup_logging, get_logger
from reverie_cli.core.exceptions import ReverieError, ModelError, AgentError

__all__ = [
    "Settings",
    "get_settings", 
    "setup_logging",
    "get_logger",
    "ReverieError",
    "ModelError", 
    "AgentError",
]
