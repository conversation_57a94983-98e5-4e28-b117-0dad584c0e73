"""
Configuration management endpoints.
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config_manager import get_config_manager


router = APIRouter()
logger = get_logger("config_api")


class ConfigUpdateRequest(BaseModel):
    """Configuration update request."""
    updates: Dict[str, Any] = Field(..., description="Configuration updates")
    persist: bool = Field(True, description="Whether to persist changes to file")


class ConfigResponse(BaseModel):
    """Configuration response."""
    config: Dict[str, Any]
    info: Dict[str, Any]


@router.get("/config", response_model=ConfigResponse)
async def get_config():
    """Get current configuration."""
    try:
        config_manager = get_config_manager()
        
        # Get effective configuration
        config = config_manager.get_effective_config()
        info = config_manager.get_config_info()
        
        return ConfigResponse(config=config, info=info)
        
    except Exception as e:
        logger.error(f"Failed to get config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get config: {e}")


@router.post("/config/update")
async def update_config(request: ConfigUpdateRequest):
    """Update configuration."""
    try:
        config_manager = get_config_manager()
        
        success = config_manager.update_config(
            updates=request.updates,
            persist=request.persist
        )
        
        if success:
            return {
                "success": True,
                "message": "Configuration updated successfully",
                "updates": request.updates
            }
        else:
            raise HTTPException(
                status_code=400,
                detail="Configuration validation failed"
            )
            
    except Exception as e:
        logger.error(f"Failed to update config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update config: {e}")


@router.post("/config/reset")
async def reset_config():
    """Reset configuration to defaults."""
    try:
        config_manager = get_config_manager()
        config_manager.reset_config()
        
        return {
            "success": True,
            "message": "Configuration reset to defaults"
        }
        
    except Exception as e:
        logger.error(f"Failed to reset config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset config: {e}")


@router.get("/config/export")
async def export_config(format: str = "yaml"):
    """Export configuration."""
    try:
        config_manager = get_config_manager()
        
        if format not in ["yaml", "json"]:
            raise HTTPException(
                status_code=400,
                detail="Format must be 'yaml' or 'json'"
            )
        
        exported_config = config_manager.export_config(format)
        
        return {
            "format": format,
            "config": exported_config
        }
        
    except Exception as e:
        logger.error(f"Failed to export config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export config: {e}")


@router.get("/config/validate")
async def validate_config():
    """Validate current configuration."""
    try:
        config_manager = get_config_manager()
        config = config_manager.get_effective_config()
        
        is_valid, errors = config_manager.validate_config(config)
        
        return {
            "valid": is_valid,
            "errors": errors,
            "config": config
        }
        
    except Exception as e:
        logger.error(f"Failed to validate config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to validate config: {e}")
