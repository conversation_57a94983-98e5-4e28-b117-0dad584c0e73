[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "reverie-cli"
version = "0.1.0"
description = "AI-native code development server with Augment-like capabilities"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Rilance Code Studio", email = "<EMAIL>"}
]
keywords = ["ai", "coding", "assistant", "cli", "agent", "development"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.8"
dependencies = [
    # Core web framework
    "fastapi",
    "uvicorn[standard]",

    # AI and ML libraries
    "transformers",
    "torch",
    "accelerate",
    "bitsandbytes",
    "sentence-transformers",

    # Database and storage
    "sqlalchemy",
    "alembic",
    "aiosqlite",

    # HTTP and networking
    "httpx",
    "aiofiles",
    "websockets",

    # CLI and console
    "rich",
    "typer",
    "prompt-toolkit",
    "click",

    # Configuration and validation
    "pydantic",
    "pydantic-settings",
    "python-dotenv",
    "pyyaml",

    # Logging and monitoring
    "loguru",
    "psutil",

    # Code analysis and tools
    "tree-sitter",
    "tree-sitter-python",
    "tree-sitter-javascript",
    "gitpython",

    # Utilities
    "python-multipart",
    "jinja2",
    "markupsafe",
    "python-magic",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
    "black",
    "isort",
    "flake8",
    "mypy",
    "pre-commit",
]
vllm = [
    "vllm",
]
gguf = [
    "llama-cpp-python",
]
tensorflow = [
    "tensorflow",
]

[project.urls]
Homepage = "https://github.com/rilance/reverie-cli"
Documentation = "https://reverie-cli.readthedocs.io"
Repository = "https://github.com/rilance/reverie-cli"
Issues = "https://github.com/rilance/reverie-cli/issues"

[project.scripts]
reverie = "reverie_cli.main:main"
reverie-cli = "reverie_cli.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["reverie_cli*"]

[tool.setuptools.package-data]
reverie_cli = [
    "static/**/*",
    "templates/**/*",
    "resources/**/*",
    "config/**/*",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["reverie_cli"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=reverie_cli",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"
