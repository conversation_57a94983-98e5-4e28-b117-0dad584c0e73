"""
Enhanced memory management for AI agents.

This module provides advanced memory capabilities including:
- Short-term and long-term memory storage
- Semantic memory search and retrieval
- Context management and learning
- Pattern recognition and user preference learning
- Intelligent memory consolidation and optimization
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from enum import Enum

from reverie_cli.core.logging import get_logger


class MemoryType(str, Enum):
    """Memory type enumeration."""
    CONVERSATION = "conversation"
    TASK = "task"
    CODE = "code"
    FILE = "file"
    TOOL_USAGE = "tool_usage"
    ERROR = "error"


@dataclass
class MemoryItem:
    """Memory item dataclass."""
    id: str
    type: MemoryType
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    importance: float = 0.5  # 0.0 to 1.0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "type": self.type.value,
            "content": self.content,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "importance": self.importance,
            "tags": self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryItem":
        """Create from dictionary."""
        return cls(
            id=data["id"],
            type=MemoryType(data["type"]),
            content=data["content"],
            metadata=data["metadata"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            importance=data["importance"],
            tags=data["tags"]
        )


class MemoryManager:
    """
    Memory management system for AI agents.
    
    Handles short-term and long-term memory, context retrieval,
    and memory consolidation.
    """
    
    def __init__(self, max_items: int = 1000):
        self.logger = get_logger("memory_manager")
        self.max_items = max_items
        
        # Memory storage
        self.memories: Dict[str, MemoryItem] = {}
        self.conversation_history: List[MemoryItem] = []
        
        # Indexes for fast retrieval
        self.type_index: Dict[MemoryType, List[str]] = {t: [] for t in MemoryType}
        self.tag_index: Dict[str, List[str]] = {}
        
        # Enhanced learning capabilities
        self.user_patterns: Dict[str, Any] = {}
        self.code_patterns: Dict[str, int] = {}
        self.preference_scores: Dict[str, float] = {}
        self.learning_enabled = True

        self.logger.info("Enhanced MemoryManager initialized")
    
    def add_memory(
        self,
        content: str,
        memory_type: MemoryType,
        metadata: Optional[Dict[str, Any]] = None,
        importance: float = 0.5,
        tags: Optional[List[str]] = None
    ) -> str:
        """
        Add a new memory item.
        
        Args:
            content: Memory content
            memory_type: Type of memory
            metadata: Additional metadata
            importance: Importance score (0.0 to 1.0)
            tags: Associated tags
            
        Returns:
            Memory ID
        """
        import uuid
        
        memory_id = str(uuid.uuid4())
        memory = MemoryItem(
            id=memory_id,
            type=memory_type,
            content=content,
            metadata=metadata or {},
            timestamp=datetime.now(),
            importance=importance,
            tags=tags or []
        )
        
        # Store memory
        self.memories[memory_id] = memory
        
        # Update indexes
        self.type_index[memory_type].append(memory_id)
        for tag in memory.tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            self.tag_index[tag].append(memory_id)
        
        # Add to conversation history if applicable
        if memory_type == MemoryType.CONVERSATION:
            self.conversation_history.append(memory)
        
        # Cleanup if needed
        self._cleanup_old_memories()
        
        self.logger.debug(f"Added memory: {memory_id} ({memory_type.value})")
        return memory_id
    
    def get_memory(self, memory_id: str) -> Optional[MemoryItem]:
        """Get a specific memory by ID."""
        return self.memories.get(memory_id)
    
    def search_memories(
        self,
        query: str,
        memory_type: Optional[MemoryType] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[MemoryItem]:
        """
        Search memories by content, type, and tags.
        
        Args:
            query: Search query
            memory_type: Filter by memory type
            tags: Filter by tags
            limit: Maximum results
            
        Returns:
            List of matching memories
        """
        results = []
        query_lower = query.lower()
        
        for memory in self.memories.values():
            # Type filter
            if memory_type and memory.type != memory_type:
                continue
            
            # Tag filter
            if tags and not any(tag in memory.tags for tag in tags):
                continue
            
            # Content search
            if query_lower in memory.content.lower():
                results.append(memory)
        
        # Sort by importance and recency
        results.sort(key=lambda m: (m.importance, m.timestamp), reverse=True)
        return results[:limit]
    
    def get_recent_memories(
        self,
        memory_type: Optional[MemoryType] = None,
        hours: int = 24,
        limit: int = 50
    ) -> List[MemoryItem]:
        """Get recent memories within specified time window."""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        memories = []
        for memory in self.memories.values():
            if memory.timestamp >= cutoff:
                if memory_type is None or memory.type == memory_type:
                    memories.append(memory)
        
        memories.sort(key=lambda m: m.timestamp, reverse=True)
        return memories[:limit]
    
    def get_conversation_context(self, limit: int = 20) -> List[MemoryItem]:
        """Get recent conversation context."""
        return self.conversation_history[-limit:]
    
    def get_memories_by_tag(self, tag: str, limit: int = 10) -> List[MemoryItem]:
        """Get memories by tag."""
        memory_ids = self.tag_index.get(tag, [])
        memories = [self.memories[mid] for mid in memory_ids if mid in self.memories]
        memories.sort(key=lambda m: (m.importance, m.timestamp), reverse=True)
        return memories[:limit]
    
    def update_memory_importance(self, memory_id: str, importance: float):
        """Update memory importance score."""
        if memory_id in self.memories:
            self.memories[memory_id].importance = max(0.0, min(1.0, importance))
            self.logger.debug(f"Updated memory importance: {memory_id} -> {importance}")
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory."""
        if memory_id not in self.memories:
            return False
        
        memory = self.memories[memory_id]
        
        # Remove from indexes
        self.type_index[memory.type].remove(memory_id)
        for tag in memory.tags:
            if tag in self.tag_index:
                self.tag_index[tag].remove(memory_id)
        
        # Remove from conversation history
        if memory in self.conversation_history:
            self.conversation_history.remove(memory)
        
        # Delete memory
        del self.memories[memory_id]
        
        self.logger.debug(f"Deleted memory: {memory_id}")
        return True
    
    def _cleanup_old_memories(self):
        """Clean up old memories if limit exceeded."""
        if len(self.memories) <= self.max_items:
            return
        
        # Sort by importance and age
        memories_list = list(self.memories.values())
        memories_list.sort(key=lambda m: (m.importance, m.timestamp))
        
        # Remove least important/oldest memories
        to_remove = len(memories_list) - self.max_items
        for memory in memories_list[:to_remove]:
            self.delete_memory(memory.id)
        
        self.logger.info(f"Cleaned up {to_remove} old memories")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        type_counts = {t.value: len(ids) for t, ids in self.type_index.items()}
        
        return {
            "total_memories": len(self.memories),
            "conversation_history_length": len(self.conversation_history),
            "type_distribution": type_counts,
            "total_tags": len(self.tag_index),
            "max_items": self.max_items
        }
    
    def export_memories(self) -> List[Dict[str, Any]]:
        """Export all memories to dictionary format."""
        return [memory.to_dict() for memory in self.memories.values()]
    
    def import_memories(self, memories_data: List[Dict[str, Any]]):
        """Import memories from dictionary format."""
        for data in memories_data:
            try:
                memory = MemoryItem.from_dict(data)
                self.memories[memory.id] = memory
                
                # Rebuild indexes
                self.type_index[memory.type].append(memory.id)
                for tag in memory.tags:
                    if tag not in self.tag_index:
                        self.tag_index[tag] = []
                    self.tag_index[tag].append(memory.id)
                
                if memory.type == MemoryType.CONVERSATION:
                    self.conversation_history.append(memory)
                    
            except Exception as e:
                self.logger.error(f"Failed to import memory: {e}")
        
        # Sort conversation history
        self.conversation_history.sort(key=lambda m: m.timestamp)
        
        self.logger.info(f"Imported {len(memories_data)} memories")

    def learn_from_interaction(
        self,
        user_input: str,
        ai_response: str,
        user_feedback: Optional[str] = None,
        success: bool = True
    ):
        """
        Learn from user interactions to improve future responses.

        Args:
            user_input: User's input/request
            ai_response: AI's response
            user_feedback: Optional user feedback
            success: Whether the interaction was successful
        """
        if not self.learning_enabled:
            return

        try:
            # Extract patterns from user input
            self._extract_user_patterns(user_input)

            # Learn from successful interactions
            if success:
                self._update_preference_scores(user_input, ai_response, 1.0)
            else:
                self._update_preference_scores(user_input, ai_response, -0.5)

            # Process user feedback
            if user_feedback:
                self._process_user_feedback(user_feedback, ai_response)

            # Update code patterns if applicable
            if any(keyword in user_input.lower() for keyword in ["code", "function", "class", "implement"]):
                self._extract_code_patterns(user_input, ai_response)

        except Exception as e:
            self.logger.error(f"Failed to learn from interaction: {e}")

    def get_user_preferences(self) -> Dict[str, Any]:
        """Get learned user preferences."""
        return {
            "patterns": self.user_patterns,
            "code_patterns": self.code_patterns,
            "preference_scores": self.preference_scores
        }

    def _extract_user_patterns(self, user_input: str):
        """Extract patterns from user input."""
        # Common request patterns
        patterns = {
            "prefers_detailed_explanations": len(user_input.split()) > 20,
            "asks_for_examples": "example" in user_input.lower(),
            "requests_code": any(word in user_input.lower() for word in ["code", "implement", "write", "create"]),
            "asks_for_help": any(word in user_input.lower() for word in ["help", "how", "what", "why"]),
            "prefers_step_by_step": any(phrase in user_input.lower() for phrase in ["step by step", "guide", "tutorial"])
        }

        for pattern, detected in patterns.items():
            if detected:
                self.user_patterns[pattern] = self.user_patterns.get(pattern, 0) + 1

    def _update_preference_scores(self, user_input: str, ai_response: str, score: float):
        """Update preference scores based on interaction success."""
        # Extract key features from the interaction
        features = {
            "response_length": len(ai_response.split()),
            "includes_code": "```" in ai_response,
            "includes_examples": "example" in ai_response.lower(),
            "includes_explanation": any(word in ai_response.lower() for word in ["because", "since", "reason"]),
            "uses_tools": "tool" in ai_response.lower() or "search" in ai_response.lower()
        }

        for feature, present in features.items():
            if present:
                current_score = self.preference_scores.get(feature, 0.0)
                self.preference_scores[feature] = current_score + (score * 0.1)

    def _process_user_feedback(self, feedback: str, ai_response: str):
        """Process explicit user feedback."""
        feedback_lower = feedback.lower()

        # Positive feedback indicators
        positive_indicators = ["good", "great", "perfect", "excellent", "helpful", "thanks"]
        negative_indicators = ["bad", "wrong", "incorrect", "unhelpful", "confusing"]

        if any(indicator in feedback_lower for indicator in positive_indicators):
            self._update_preference_scores("", ai_response, 1.0)
        elif any(indicator in feedback_lower for indicator in negative_indicators):
            self._update_preference_scores("", ai_response, -1.0)

    def _extract_code_patterns(self, user_input: str, ai_response: str):
        """Extract code-related patterns."""
        # Programming languages mentioned
        languages = ["python", "javascript", "java", "cpp", "rust", "go", "typescript"]
        for lang in languages:
            if lang in user_input.lower() or lang in ai_response.lower():
                self.code_patterns[f"uses_{lang}"] = self.code_patterns.get(f"uses_{lang}", 0) + 1

        # Code patterns
        if "class" in ai_response.lower():
            self.code_patterns["creates_classes"] = self.code_patterns.get("creates_classes", 0) + 1

        if "function" in ai_response.lower() or "def " in ai_response:
            self.code_patterns["creates_functions"] = self.code_patterns.get("creates_functions", 0) + 1

        if "test" in user_input.lower():
            self.code_patterns["writes_tests"] = self.code_patterns.get("writes_tests", 0) + 1

    def get_personalized_suggestions(self, context: str) -> List[str]:
        """Get personalized suggestions based on learned patterns."""
        suggestions = []

        # Based on user patterns
        if self.user_patterns.get("prefers_detailed_explanations", 0) > 5:
            suggestions.append("Provide detailed explanations with reasoning")

        if self.user_patterns.get("asks_for_examples", 0) > 3:
            suggestions.append("Include practical examples")

        if self.user_patterns.get("prefers_step_by_step", 0) > 3:
            suggestions.append("Break down into step-by-step instructions")

        # Based on code patterns
        if self.code_patterns.get("uses_python", 0) > 5:
            suggestions.append("Focus on Python-based solutions")

        if self.code_patterns.get("writes_tests", 0) > 2:
            suggestions.append("Include test cases and examples")

        return suggestions
