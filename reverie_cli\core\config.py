"""
Configuration management for Reverie CLI.

This module handles all configuration settings using Pydantic Settings,
supporting environment variables, YAML files, and runtime configuration.
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Union
from functools import lru_cache

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class ServerConfig(BaseSettings):
    """Server configuration settings."""
    
    host: str = Field(default="127.0.0.1", description="Server host address")
    port: int = Field(default=8000, description="Server port")
    reload: bool = Field(default=False, description="Enable auto-reload in development")
    workers: int = Field(default=1, description="Number of worker processes")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # CORS settings
    cors_origins: List[str] = Field(default=["*"], description="CORS allowed origins")
    cors_methods: List[str] = Field(default=["*"], description="CORS allowed methods")
    cors_headers: List[str] = Field(default=["*"], description="CORS allowed headers")


class ModelConfig(BaseSettings):
    """AI model configuration settings."""
    
    # Default model settings
    default_model: str = Field(
        default="Menlo/Lucy-128k-gguf", 
        description="Default model to load"
    )
    max_context_length: int = Field(
        default=131072, 
        description="Maximum context length (128k for Lucy)"
    )
    temperature: float = Field(default=0.7, description="Model temperature")
    max_tokens: int = Field(default=4096, description="Maximum tokens to generate")
    
    # Model backend preferences
    preferred_backend: Optional[str] = Field(
        default=None, 
        description="Preferred backend (transformers, vllm, gguf, tensorflow)"
    )
    device: str = Field(default="auto", description="Device to use (auto, cpu, cuda)")
    
    # Optimization settings
    transformers_load_in_4bit: bool = Field(
        default=True, 
        description="Use 4-bit quantization for Transformers"
    )
    transformers_load_in_8bit: bool = Field(
        default=False, 
        description="Use 8-bit quantization for Transformers"
    )
    use_flash_attention: bool = Field(
        default=True, 
        description="Use Flash Attention if available"
    )
    
    # Model cache settings
    cache_dir: str = Field(
        default="~/.cache/reverie_cli/models", 
        description="Model cache directory"
    )
    max_cache_size_gb: float = Field(
        default=50.0, 
        description="Maximum cache size in GB"
    )


class WebConfig(BaseSettings):
    """Web Engine configuration settings."""

    # Search API keys
    google_api_key: Optional[str] = Field(default=None, description="Google Custom Search API key")
    google_search_engine_id: Optional[str] = Field(default=None, description="Google Custom Search Engine ID")
    bing_api_key: Optional[str] = Field(default=None, description="Bing Search API key")

    # Search settings
    default_search_engine: str = Field(default="duckduckgo", description="Default search engine")
    max_search_results: int = Field(default=10, description="Maximum search results")
    search_timeout: int = Field(default=30, description="Search timeout in seconds")

    # Content extraction
    max_content_length: int = Field(default=10000, description="Maximum content length to extract")
    enable_content_caching: bool = Field(default=True, description="Enable content caching")
    cache_ttl_hours: int = Field(default=24, description="Cache TTL in hours")


class ContextConfig(BaseSettings):
    """Context Engine configuration settings."""

    # Analysis settings
    enable_deep_analysis: bool = Field(default=True, description="Enable deep code analysis")
    max_file_size_mb: int = Field(default=10, description="Maximum file size to analyze (MB)")
    analysis_timeout: int = Field(default=60, description="Analysis timeout in seconds")

    # Indexing settings
    enable_symbol_indexing: bool = Field(default=True, description="Enable symbol indexing")
    index_refresh_interval: int = Field(default=3600, description="Index refresh interval in seconds")
    max_symbols_per_file: int = Field(default=1000, description="Maximum symbols per file")

    # Pattern recognition
    enable_pattern_detection: bool = Field(default=True, description="Enable pattern detection")
    min_pattern_confidence: float = Field(default=0.7, description="Minimum pattern confidence")


class AgentConfig(BaseSettings):
    """AI Agent configuration settings."""

    # Agent behavior
    max_iterations: int = Field(default=50, description="Maximum agent iterations")
    max_tool_calls: int = Field(default=100, description="Maximum tool calls per session")
    timeout_seconds: int = Field(default=300, description="Agent timeout in seconds")

    # Memory and context
    memory_enabled: bool = Field(default=True, description="Enable agent memory")
    max_memory_items: int = Field(default=1000, description="Maximum memory items")
    context_window_size: int = Field(default=8192, description="Context window size")

    # Code analysis
    enable_code_analysis: bool = Field(default=True, description="Enable code analysis")
    supported_languages: List[str] = Field(
        default=["python", "javascript", "typescript", "rust", "go", "java", "cpp"],
        description="Supported programming languages"
    )

    # Enhanced capabilities
    enable_web_search: bool = Field(default=True, description="Enable web search capabilities")
    enable_context_engine: bool = Field(default=True, description="Enable context engine")
    enable_memory_learning: bool = Field(default=True, description="Enable memory learning")


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    url: str = Field(
        default="sqlite+aiosqlite:///./reverie_cli.db",
        description="Database URL"
    )
    echo: bool = Field(default=False, description="Echo SQL queries")
    pool_size: int = Field(default=5, description="Connection pool size")
    max_overflow: int = Field(default=10, description="Maximum pool overflow")


class LoggingConfig(BaseSettings):
    """Logging configuration settings."""
    
    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        description="Log format"
    )
    file_enabled: bool = Field(default=True, description="Enable file logging")
    file_path: str = Field(default="logs/reverie_cli.log", description="Log file path")
    file_rotation: str = Field(default="10 MB", description="Log file rotation")
    file_retention: str = Field(default="30 days", description="Log file retention")
    console_enabled: bool = Field(default=True, description="Enable console logging")


class Settings(BaseSettings):
    """Main settings class that combines all configuration sections."""

    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Debug mode")

    # Configuration sections
    server: ServerConfig = Field(default_factory=ServerConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    context: ContextConfig = Field(default_factory=ContextConfig)
    model: ModelConfig = Field(default_factory=ModelConfig)
    agent: AgentConfig = Field(default_factory=AgentConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # API Keys and secrets
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    google_api_key: Optional[str] = Field(default=None, description="Google API key")
    huggingface_token: Optional[str] = Field(default=None, description="HuggingFace token")
    
    # Paths
    data_dir: str = Field(default="./data", description="Data directory")
    temp_dir: str = Field(default="./temp", description="Temporary directory")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
    
    @validator("data_dir", "temp_dir", pre=True)
    def expand_paths(cls, v):
        """Expand user paths and make them absolute."""
        return str(Path(v).expanduser().resolve())
    
    def model_post_init(self, __context) -> None:
        """Post-initialization setup."""
        # Create directories if they don't exist
        for dir_path in [self.data_dir, self.temp_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        # Create logs directory
        log_dir = Path(self.logging.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
