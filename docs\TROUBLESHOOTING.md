# 🚨 Reverie CLI - 故障排除指南

## 🎯 概述

本指南帮助用户诊断和解决使用Reverie CLI时遇到的常见问题。按问题类型分类，提供详细的解决步骤。

## 🔧 环境和安装问题

### Python环境问题

#### 问题: Python未找到
```
[ERROR] Python not found
```

**解决方案**:
```bash
# 1. 检查Python是否安装
python --version
python3 --version

# 2. 如果未安装，下载安装Python 3.8+
# Windows: https://www.python.org/downloads/
# 确保勾选 "Add Python to PATH"

# 3. 验证安装
python --version
pip --version
```

#### 问题: Python版本过低
```
[ERROR] Python version 3.7 is not supported. Requires 3.8+
```

**解决方案**:
```bash
# 1. 升级Python到3.8+
# 2. 或使用pyenv管理多个Python版本

# Windows - 使用pyenv-win
git clone https://github.com/pyenv-win/pyenv-win.git %USERPROFILE%\.pyenv
# 添加到PATH并重启终端

# 安装Python 3.9
pyenv install 3.9.18
pyenv global 3.9.18
```

### 虚拟环境问题

#### 问题: 虚拟环境创建失败
```
[ERROR] Failed to create virtual environment
```

**解决方案**:
```bash
# 1. 检查磁盘空间
dir C:\ # Windows
df -h   # Linux/Mac

# 2. 检查权限
# 确保当前用户有写权限

# 3. 手动创建虚拟环境
python -m venv reverie_env --clear

# 4. 如果仍然失败，使用virtualenv
pip install virtualenv
virtualenv reverie_env
```

#### 问题: 虚拟环境激活失败
```
[ERROR] Failed to activate virtual environment
```

**解决方案**:
```bash
# Windows CMD
reverie_env\Scripts\activate.bat

# Windows PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\reverie_env\Scripts\Activate.ps1

# Linux/Mac
source reverie_env/bin/activate

# 验证激活
where python  # Windows
which python  # Linux/Mac
```

### 依赖安装问题

#### 问题: PyTorch安装失败
```
[ERROR] PyTorch installation failed
```

**解决方案**:
```bash
# 1. 检查CUDA版本
nvidia-smi

# 2. 根据CUDA版本安装PyTorch
# CUDA 12.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128

# CUDA 12.1
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# CPU版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 3. 验证安装
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
```

#### 问题: 依赖冲突
```
[ERROR] Dependencies installation failed
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed
```

**解决方案**:
```bash
# 1. 清理pip缓存
pip cache purge

# 2. 升级pip
python -m pip install --upgrade pip

# 3. 使用pip-tools解决冲突
pip install pip-tools
pip-compile requirements.txt
pip-sync requirements.txt

# 4. 强制重新安装
pip install -r requirements.txt --force-reinstall --no-deps
pip install -r requirements.txt
```

## 🚀 启动和运行问题

### 服务器启动问题

#### 问题: 端口被占用
```
[WARNING] Port 8000 is already in use
```

**解决方案**:
```bash
# 1. 查找占用端口的进程
netstat -ano | findstr :8000  # Windows
lsof -i :8000                 # Linux/Mac

# 2. 终止进程
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Linux/Mac

# 3. 或使用不同端口
start.bat --server --port 8001
```

#### 问题: 模块导入失败
```
[ERROR] ModuleNotFoundError: No module named 'reverie_cli'
```

**解决方案**:
```bash
# 1. 确保虚拟环境已激活
reverie_env\Scripts\activate.bat

# 2. 重新安装项目
pip install -e .

# 3. 检查PYTHONPATH
echo %PYTHONPATH%  # Windows
echo $PYTHONPATH   # Linux/Mac

# 4. 手动设置PYTHONPATH
set PYTHONPATH=%CD%;%PYTHONPATH%  # Windows
export PYTHONPATH=$PWD:$PYTHONPATH  # Linux/Mac
```

### 模型加载问题

#### 问题: 模型加载失败
```
[ERROR] Failed to load model: lucy-128k
```

**解决方案**:
```bash
# 1. 检查模型文件
python -c "
from reverie_cli.models.manager import ModelManager
manager = ModelManager()
print(manager.list_available_models())
"

# 2. 检查GPU内存
python -c "
import torch
if torch.cuda.is_available():
    print(f'GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB')
    print(f'GPU Free: {torch.cuda.memory_reserved(0) / 1024**3:.1f}GB')
"

# 3. 使用CPU模式
python -c "
from reverie_cli.config.settings import settings
settings.USE_GPU = False
"
```

#### 问题: GPU内存不足
```
[ERROR] CUDA out of memory
```

**解决方案**:
```bash
# 1. 减少批处理大小
config set BATCH_SIZE 1

# 2. 使用模型量化
config set USE_QUANTIZATION true

# 3. 限制GPU内存使用
config set GPU_MEMORY_FRACTION 0.5

# 4. 使用CPU模式
config set USE_GPU false
```

## 🌐 网络和连接问题

### Web Engine问题

#### 问题: 网络搜索失败
```
[ERROR] Web search failed: Connection timeout
```

**解决方案**:
```bash
# 1. 检查网络连接
ping google.com
ping bing.com

# 2. 检查代理设置
echo %HTTP_PROXY%   # Windows
echo $HTTP_PROXY    # Linux/Mac

# 3. 配置代理
config set HTTP_PROXY "http://proxy.company.com:8080"
config set HTTPS_PROXY "https://proxy.company.com:8080"

# 4. 使用不同搜索引擎
web search "test" --engines duckduckgo,searx
```

#### 问题: SSL证书错误
```
[ERROR] SSL: CERTIFICATE_VERIFY_FAILED
```

**解决方案**:
```bash
# 1. 更新证书
pip install --upgrade certifi

# 2. 临时禁用SSL验证 (不推荐)
config set VERIFY_SSL false

# 3. 使用企业证书
config set CA_BUNDLE_PATH "/path/to/company-ca.pem"
```

### API连接问题

#### 问题: API请求超时
```
[ERROR] Request timeout after 30 seconds
```

**解决方案**:
```bash
# 1. 增加超时时间
config set REQUEST_TIMEOUT 60

# 2. 检查服务器状态
curl http://localhost:8000/api/v1/health

# 3. 重启服务器
start.bat --server
```

## 💾 数据和存储问题

### 数据库问题

#### 问题: 数据库连接失败
```
[ERROR] Database connection failed
```

**解决方案**:
```bash
# 1. 检查数据库文件
dir data\*.db     # Windows
ls data/*.db      # Linux/Mac

# 2. 重新初始化数据库
python -c "
from reverie_cli.tools.memory_engine import MemoryEngine
engine = MemoryEngine()
engine._init_database()
"

# 3. 检查权限
# 确保data目录有写权限
```

#### 问题: 数据库损坏
```
[ERROR] Database disk image is malformed
```

**解决方案**:
```bash
# 1. 备份数据库
copy data\memory.db data\memory.db.backup  # Windows
cp data/memory.db data/memory.db.backup    # Linux/Mac

# 2. 修复数据库
sqlite3 data/memory.db ".recover" | sqlite3 data/memory_recovered.db

# 3. 替换数据库
move data\memory_recovered.db data\memory.db  # Windows
mv data/memory_recovered.db data/memory.db    # Linux/Mac
```

### 缓存问题

#### 问题: 缓存空间不足
```
[WARNING] Cache directory full
```

**解决方案**:
```bash
# 1. 清理缓存
python -c "
from reverie_cli.tools.web_engine import WebEngine
engine = WebEngine()
engine._clear_cache()
"

# 2. 配置缓存大小
config set CACHE_MAX_SIZE 1000  # MB

# 3. 禁用缓存
config set USE_CACHE false
```

## 🧠 AI和模型问题

### 响应质量问题

#### 问题: AI响应质量差
```
AI responses are not helpful or accurate
```

**解决方案**:
```bash
# 1. 调整温度参数
config set TEMPERATURE 0.3  # 更确定性
config set TEMPERATURE 0.9  # 更创造性

# 2. 增加上下文长度
config set MAX_CONTEXT_LENGTH 8192

# 3. 使用更好的模型
models load gpt-4
models load claude-3-sonnet

# 4. 改进提示
# 在控制台中使用更具体的提示
```

#### 问题: 响应速度慢
```
AI responses take too long to generate
```

**解决方案**:
```bash
# 1. 减少最大token数
config set MAX_TOKENS 1000

# 2. 使用更快的模型
models load lucy-128k-fast

# 3. 启用GPU加速
config set USE_GPU true

# 4. 调整批处理大小
config set BATCH_SIZE 4
```

### 内存使用问题

#### 问题: 内存使用过高
```
[WARNING] High memory usage detected
```

**解决方案**:
```bash
# 1. 监控内存使用
python -c "
import psutil
process = psutil.Process()
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.1f}MB')
"

# 2. 清理内存
python -c "
import gc
gc.collect()
"

# 3. 重启服务
start.bat --server

# 4. 调整配置
config set MEMORY_OPTIMIZATION true
config set CACHE_SIZE 500
```

## 🔍 调试和诊断

### 启用调试模式
```bash
# 1. 设置日志级别
config set LOG_LEVEL DEBUG

# 2. 查看详细日志
type logs\reverie_cli.log     # Windows
tail -f logs/reverie_cli.log  # Linux/Mac

# 3. 启用性能监控
config set PERFORMANCE_MONITORING true
```

### 收集诊断信息
```bash
# 运行诊断脚本
python -c "
import sys
import platform
import torch
from reverie_cli import __version__

print('=== Reverie CLI 诊断信息 ===')
print(f'版本: {__version__}')
print(f'Python: {sys.version}')
print(f'平台: {platform.platform()}')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA版本: {torch.version.cuda}')
    print(f'GPU数量: {torch.cuda.device_count()}')
"
```

### 性能分析
```bash
# 使用性能分析器
python -m cProfile -o profile.stats -m reverie_cli.main

# 分析结果
python -c "
import pstats
stats = pstats.Stats('profile.stats')
stats.sort_stats('cumulative').print_stats(20)
"
```

## 📞 获取帮助

### 自助资源
1. **文档**: 查看 `docs/` 目录下的完整文档
2. **日志**: 检查 `logs/reverie_cli.log` 文件
3. **配置**: 运行 `config show` 查看当前配置

### 社区支持
1. **GitHub Issues**: 报告bug和功能请求
2. **讨论区**: 技术讨论和问答
3. **Wiki**: 社区维护的知识库

### 报告问题
提交问题时请包含:
1. **错误信息**: 完整的错误日志
2. **环境信息**: 操作系统、Python版本等
3. **重现步骤**: 详细的重现步骤
4. **配置信息**: 相关的配置设置

```bash
# 生成问题报告
python -c "
import json
from reverie_cli.config.settings import settings

report = {
    'version': '2.0.0',
    'python_version': sys.version,
    'platform': platform.platform(),
    'config': dict(settings),
    'error': 'paste error message here'
}

print(json.dumps(report, indent=2))
"
```

这个故障排除指南涵盖了用户可能遇到的大部分问题，并提供了详细的解决步骤。
