# 💻 Reverie CLI - CMD/PowerShell 调用指南

## 🎯 概述

本指南详细介绍如何通过CMD和PowerShell直接调用Reverie CLI的各种功能，包括Web Engine、Context Engine、Memory Engine等。

## 🚀 环境准备

### 激活虚拟环境
```cmd
# CMD
reverie_env\Scripts\activate.bat

# PowerShell
.\reverie_env\Scripts\Activate.ps1
```

### 设置环境变量
```cmd
# CMD
set PYTHONPATH=%CD%;%PYTHONPATH%

# PowerShell
$env:PYTHONPATH = "$PWD;$env:PYTHONPATH"
```

## 🌐 Web Engine 命令行调用

### 基本搜索功能

#### 智能搜索
```cmd
# CMD - 基本搜索
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def search():
    engine = WebEngine()
    result = await engine.smart_search('FastAPI最佳实践', max_results=5)
    print(result.data if result.success else result.error)

asyncio.run(search())
"

# PowerShell - 基本搜索
python -c @"
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def search():
    engine = WebEngine()
    result = await engine.smart_search('Python异步编程', max_results=3)
    if result.success:
        for i, item in enumerate(result.data.get('results', []), 1):
            print(f'{i}. {item.get(\"title\", \"No title\")}')
            print(f'   {item.get(\"url\", \"No URL\")}')
            print(f'   {item.get(\"snippet\", \"No snippet\")}')
            print()
    else:
        print(f'Error: {result.error}')

asyncio.run(search())
"@
```

#### 多引擎搜索
```cmd
# CMD - 多引擎搜索
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def multi_search():
    engine = WebEngine()
    
    # 搜索编程相关内容
    queries = ['Python web框架', 'API设计模式', '数据库优化']
    
    for query in queries:
        print(f'搜索: {query}')
        result = await engine.smart_search(query, max_results=3)
        if result.success:
            results = result.data.get('results', [])
            for i, item in enumerate(results, 1):
                print(f'  {i}. {item.get(\"title\", \"\")}')
        print()

asyncio.run(multi_search())
"
```

#### 内容提取
```cmd
# CMD - 网页内容提取
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def extract_content():
    engine = WebEngine()
    url = 'https://fastapi.tiangolo.com/'
    
    result = await engine.intelligent_extract(url, extract_type='smart')
    if result.success:
        content = result.data.get('content', '')
        print(f'提取的内容 ({len(content)} 字符):')
        print(content[:500] + '...' if len(content) > 500 else content)
    else:
        print(f'提取失败: {result.error}')

asyncio.run(extract_content())
"
```

### 高级Web功能

#### 批量URL处理
```powershell
# PowerShell - 批量处理URL
$urls = @(
    "https://docs.python.org/3/library/asyncio.html",
    "https://fastapi.tiangolo.com/tutorial/",
    "https://jwt.io/introduction/"
)

python -c @"
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def batch_extract():
    engine = WebEngine()
    urls = ['$($urls -join "', '")']
    
    for url in urls:
        print(f'处理: {url}')
        result = await engine.intelligent_extract(url, extract_type='smart')
        if result.success:
            content = result.data.get('content', '')
            print(f'  提取成功: {len(content)} 字符')
        else:
            print(f'  提取失败: {result.error}')
        print()

asyncio.run(batch_extract())
"@
```

#### 搜索结果分析
```cmd
# CMD - 搜索结果分析和过滤
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def analyze_search():
    engine = WebEngine()
    query = 'Python机器学习库'
    
    result = await engine.smart_search(query, max_results=10, include_analysis=True)
    if result.success:
        results = result.data.get('results', [])
        analysis = result.data.get('analysis', {})
        
        print(f'搜索查询: {query}')
        print(f'找到结果: {len(results)} 个')
        print(f'平均相关性: {analysis.get(\"avg_relevance\", 0):.2f}')
        print()
        
        # 按相关性排序
        sorted_results = sorted(results, 
                              key=lambda x: x.get('relevance_score', 0), 
                              reverse=True)
        
        print('Top 5 最相关结果:')
        for i, item in enumerate(sorted_results[:5], 1):
            score = item.get('relevance_score', 0)
            title = item.get('title', 'No title')
            print(f'{i}. [{score:.2f}] {title}')

asyncio.run(analyze_search())
"
```

## 🧠 Context Engine 命令行调用

### 项目分析

#### 基本项目分析
```cmd
# CMD - 分析当前项目
python -c "
import asyncio
from reverie_cli.tools.context_engine import ContextEngine

async def analyze_project():
    engine = ContextEngine()
    project_path = '.'
    
    result = await engine.smart_analyze(
        project_path=project_path,
        analysis_type='quick',
        include_ai_insights=True
    )
    
    if result.success:
        data = result.data
        print(f'项目分析结果:')
        print(f'  语言: {data.get(\"language\", \"Unknown\")}')
        print(f'  框架: {data.get(\"framework\", \"None\")}')
        print(f'  文件数: {data.get(\"file_count\", 0)}')
        print(f'  代码行数: {data.get(\"total_lines\", 0)}')
        print(f'  符号数: {data.get(\"symbol_count\", 0)}')
        
        if 'ai_insights' in data:
            print(f'  AI洞察: {data[\"ai_insights\"]}')
    else:
        print(f'分析失败: {result.error}')

asyncio.run(analyze_project())
"
```

#### 深度代码分析
```powershell
# PowerShell - 深度分析指定目录
$targetPath = ".\src"

python -c @"
import asyncio
from reverie_cli.tools.context_engine import ContextEngine

async def deep_analysis():
    engine = ContextEngine()
    
    result = await engine.smart_analyze(
        project_path='$targetPath',
        analysis_type='comprehensive',
        include_ai_insights=True
    )
    
    if result.success:
        data = result.data
        print('=== 深度代码分析报告 ===')
        print(f'分析路径: $targetPath')
        print(f'语言: {data.get(\"language\", \"Unknown\")}')
        print(f'框架: {data.get(\"framework\", \"None\")}')
        print()
        
        # 文件统计
        print('文件统计:')
        print(f'  总文件数: {data.get(\"file_count\", 0)}')
        print(f'  代码文件: {data.get(\"code_files\", 0)}')
        print(f'  总代码行: {data.get(\"total_lines\", 0)}')
        print()
        
        # 代码质量
        if 'quality_metrics' in data:
            metrics = data['quality_metrics']
            print('代码质量:')
            print(f'  复杂度评分: {metrics.get(\"complexity_score\", 0):.2f}')
            print(f'  可维护性: {metrics.get(\"maintainability\", 0):.2f}')
            print(f'  代码覆盖率: {metrics.get(\"coverage\", 0):.2f}%')
            print()
        
        # 依赖分析
        if 'dependencies' in data:
            deps = data['dependencies']
            print(f'依赖包数量: {len(deps)}')
            if deps:
                print('主要依赖:')
                for dep in deps[:5]:
                    print(f'  - {dep}')
    else:
        print(f'分析失败: {result.error}')

asyncio.run(deep_analysis())
"@
```

### 代码质量检查

#### 质量评估
```cmd
# CMD - 代码质量评估
python -c "
import asyncio
from reverie_cli.tools.context_engine import ContextEngine
import os

async def quality_check():
    engine = ContextEngine()
    
    # 检查所有Python文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                file_path = os.path.join(root, file)
                
                try:
                    # 分析单个文件
                    symbols, relationships = await engine._parse_python_file_enhanced(file_path)
                    
                    print(f'文件: {file_path}')
                    print(f'  符号数: {len(symbols)}')
                    
                    # 计算平均复杂度
                    complexities = [s.complexity_score for s in symbols.values() 
                                  if hasattr(s, 'complexity_score')]
                    if complexities:
                        avg_complexity = sum(complexities) / len(complexities)
                        print(f'  平均复杂度: {avg_complexity:.2f}')
                    
                    # 计算平均质量分数
                    qualities = [s.quality_score for s in symbols.values() 
                               if hasattr(s, 'quality_score')]
                    if qualities:
                        avg_quality = sum(qualities) / len(qualities)
                        print(f'  平均质量: {avg_quality:.2f}')
                    
                    print()
                except Exception as e:
                    print(f'分析 {file_path} 失败: {e}')

asyncio.run(quality_check())
"
```

## 💾 Memory Engine 命令行调用

### 记忆存储和检索

#### 存储信息
```cmd
# CMD - 存储项目信息
python -c "
import asyncio
from reverie_cli.tools.memory_engine import MemoryEngine

async def store_info():
    engine = MemoryEngine()
    
    # 存储项目相关信息
    memories = [
        ('项目使用FastAPI框架进行API开发', 'project', 0.8, ['fastapi', 'api', 'web']),
        ('数据库使用PostgreSQL存储用户数据', 'database', 0.7, ['postgresql', 'database', 'users']),
        ('认证系统使用JWT token实现', 'security', 0.9, ['jwt', 'auth', 'security']),
        ('部署使用Docker容器化', 'deployment', 0.6, ['docker', 'deployment', 'containers'])
    ]
    
    for content, mem_type, importance, tags in memories:
        result = await engine.smart_remember(
            content=content,
            memory_type=mem_type,
            importance=importance,
            tags=tags
        )
        
        if result.success:
            print(f'存储成功: {content[:30]}...')
        else:
            print(f'存储失败: {result.error}')

asyncio.run(store_info())
"
```

#### 智能搜索
```powershell
# PowerShell - 智能搜索记忆
python -c @"
import asyncio
from reverie_cli.tools.memory_engine import MemoryEngine

async def search_memories():
    engine = MemoryEngine()
    
    queries = ['FastAPI', 'JWT认证', '数据库设计', 'Docker部署']
    
    for query in queries:
        print(f'搜索: {query}')
        result = await engine.intelligent_search(
            query=query,
            search_type='smart',
            max_results=3
        )
        
        if result.success:
            results = result.data.get('results', [])
            print(f'  找到 {len(results)} 个相关记忆:')
            
            for i, item in enumerate(results, 1):
                memory = item.get('item', {})
                score = item.get('similarity_score', 0)
                content = memory.get('content', '')
                print(f'    {i}. [{score:.2f}] {content[:50]}...')
        else:
            print(f'  搜索失败: {result.error}')
        print()

asyncio.run(search_memories())
"@
```

### 记忆管理

#### 记忆统计
```cmd
# CMD - 记忆统计信息
python -c "
import asyncio
from reverie_cli.tools.memory_engine import MemoryEngine

async def memory_stats():
    engine = MemoryEngine()
    
    # 获取所有记忆
    result = await engine.intelligent_search(
        query='',
        search_type='all',
        max_results=1000
    )
    
    if result.success:
        memories = result.data.get('results', [])
        print(f'总记忆数量: {len(memories)}')
        
        # 按类型统计
        types = {}
        importances = []
        
        for item in memories:
            memory = item.get('item', {})
            mem_type = memory.get('memory_type', 'unknown')
            importance = memory.get('importance', 0)
            
            types[mem_type] = types.get(mem_type, 0) + 1
            importances.append(importance)
        
        print('按类型分布:')
        for mem_type, count in types.items():
            print(f'  {mem_type}: {count}')
        
        if importances:
            avg_importance = sum(importances) / len(importances)
            print(f'平均重要性: {avg_importance:.2f}')
    else:
        print(f'获取统计失败: {result.error}')

asyncio.run(memory_stats())
"
```

## 🎪 组合操作示例

### 智能项目分析
```cmd
# CMD - 组合Web搜索和项目分析
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.tools.memory_engine import MemoryEngine

async def smart_project_analysis():
    web_engine = WebEngine()
    context_engine = ContextEngine()
    memory_engine = MemoryEngine()
    
    print('=== 智能项目分析 ===')
    
    # 1. 分析项目结构
    print('1. 分析项目结构...')
    context_result = await context_engine.smart_analyze('.', 'quick', True)
    
    if context_result.success:
        data = context_result.data
        language = data.get('language', 'Unknown')
        framework = data.get('framework', 'None')
        
        print(f'   语言: {language}')
        print(f'   框架: {framework}')
        
        # 2. 搜索相关最佳实践
        if framework != 'None':
            print(f'2. 搜索 {framework} 最佳实践...')
            web_result = await web_engine.smart_search(
                f'{framework} 最佳实践 {language}', 
                max_results=3
            )
            
            if web_result.success:
                results = web_result.data.get('results', [])
                print(f'   找到 {len(results)} 个相关资源')
                
                # 3. 存储到记忆中
                for result in results:
                    title = result.get('title', '')
                    url = result.get('url', '')
                    content = f'{title} - {url}'
                    
                    await memory_engine.smart_remember(
                        content=content,
                        memory_type='best_practices',
                        importance=0.7,
                        tags=[framework.lower(), language.lower(), 'best_practices']
                    )
                
                print('   最佳实践已存储到记忆中')
    
    print('分析完成!')

asyncio.run(smart_project_analysis())
"
```

### 自动化代码审查
```powershell
# PowerShell - 自动化代码审查流程
python -c @"
import asyncio
import os
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.memory_engine import MemoryEngine

async def automated_code_review():
    context_engine = ContextEngine()
    web_engine = WebEngine()
    memory_engine = MemoryEngine()
    
    print('=== 自动化代码审查 ===')
    
    # 1. 分析代码质量
    print('1. 分析代码质量...')
    analysis_result = await context_engine.smart_analyze(
        '.', 'comprehensive', True
    )
    
    if analysis_result.success:
        data = analysis_result.data
        language = data.get('language', 'Unknown')
        
        # 检查是否有质量问题
        quality_metrics = data.get('quality_metrics', {})
        complexity = quality_metrics.get('complexity_score', 0)
        maintainability = quality_metrics.get('maintainability', 0)
        
        print(f'   复杂度评分: {complexity:.2f}')
        print(f'   可维护性: {maintainability:.2f}')
        
        issues = []
        if complexity > 7:
            issues.append('代码复杂度过高')
        if maintainability < 6:
            issues.append('可维护性较低')
        
        # 2. 如果有问题，搜索解决方案
        if issues:
            print('2. 发现问题，搜索解决方案...')
            for issue in issues:
                print(f'   问题: {issue}')
                
                search_query = f'{language} {issue} 解决方案'
                web_result = await web_engine.smart_search(search_query, max_results=2)
                
                if web_result.success:
                    results = web_result.data.get('results', [])
                    for result in results:
                        title = result.get('title', '')
                        print(f'     解决方案: {title}')
                        
                        # 存储解决方案
                        await memory_engine.smart_remember(
                            content=f'问题: {issue} - 解决方案: {title}',
                            memory_type='code_review',
                            importance=0.8,
                            tags=['code_quality', 'solutions', language.lower()]
                        )
        else:
            print('2. 代码质量良好，无需改进')
    
    print('代码审查完成!')

asyncio.run(automated_code_review())
"@
```

## 🔧 实用脚本模板

### 批处理脚本模板
```cmd
@echo off
setlocal enabledelayedexpansion

:: 激活虚拟环境
call reverie_env\Scripts\activate.bat

:: 设置环境变量
set PYTHONPATH=%CD%;%PYTHONPATH%

:: 执行Python脚本
python -c "
import asyncio
from reverie_cli.tools.web_engine import WebEngine

async def main():
    engine = WebEngine()
    # 在这里添加你的代码
    pass

asyncio.run(main())
"

pause
```

### PowerShell脚本模板
```powershell
# 激活虚拟环境
& .\reverie_env\Scripts\Activate.ps1

# 设置环境变量
$env:PYTHONPATH = "$PWD;$env:PYTHONPATH"

# 执行Python脚本
python -c @"
import asyncio
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.tools.memory_engine import MemoryEngine

async def main():
    # 在这里添加你的代码
    pass

asyncio.run(main())
"@

Read-Host "Press Enter to continue..."
```

## 📊 性能监控脚本

### 系统资源监控
```cmd
# CMD - 监控系统资源使用
python -c "
import asyncio
import psutil
import time
from reverie_cli.tools.web_engine import WebEngine

async def monitor_performance():
    print('=== 性能监控 ===')
    
    # 监控5次，每次间隔2秒
    for i in range(5):
        print(f'监控周期 {i+1}/5:')
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f'  CPU使用率: {cpu_percent}%')
        
        # 内存使用
        memory = psutil.virtual_memory()
        print(f'  内存使用: {memory.percent}% ({memory.used//1024//1024}MB/{memory.total//1024//1024}MB)')
        
        # 测试Web引擎性能
        start_time = time.time()
        engine = WebEngine()
        result = await engine.smart_search('test', max_results=1)
        end_time = time.time()
        
        if result.success:
            print(f'  Web搜索延迟: {(end_time - start_time)*1000:.2f}ms')
        else:
            print(f'  Web搜索失败: {result.error}')
        
        print()
        time.sleep(2)

asyncio.run(monitor_performance())
"
```

这个指南提供了通过CMD和PowerShell直接调用Reverie CLI各种功能的详细方法。用户可以根据需要选择合适的方式来集成到他们的工作流程中。
