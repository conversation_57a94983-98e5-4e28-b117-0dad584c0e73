"""
Task planning and decomposition for AI agents.
"""

import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from reverie_cli.core.logging import get_logger
from reverie_cli.models.manager import get_model_manager
from reverie_cli.tools.manager import get_tool_manager


class TaskType(str, Enum):
    """Task type enumeration."""
    CODE_GENERATION = "code_generation"
    FILE_OPERATION = "file_operation"
    ANALYSIS = "analysis"
    RESEARCH = "research"
    EXECUTION = "execution"
    COMMUNICATION = "communication"


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class Task:
    """Task representation."""
    id: str
    description: str
    task_type: TaskType
    status: TaskStatus = TaskStatus.PENDING
    dependencies: List[str] = None
    tools_required: List[str] = None
    estimated_complexity: float = 0.5  # 0.0 to 1.0
    result: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.tools_required is None:
            self.tools_required = []
        if self.metadata is None:
            self.metadata = {}


class TaskPlanner:
    """
    Task planning and decomposition system.
    
    Breaks down complex user requests into executable tasks
    and determines the optimal execution order.
    """
    
    def __init__(self):
        self.logger = get_logger("task_planner")
        
        # Task patterns for recognition
        self.task_patterns = {
            TaskType.CODE_GENERATION: [
                r"write.*code", r"create.*function", r"implement.*algorithm",
                r"generate.*script", r"build.*program", r"develop.*application"
            ],
            TaskType.FILE_OPERATION: [
                r"create.*file", r"read.*file", r"write.*file", r"delete.*file",
                r"save.*to", r"load.*from", r"copy.*file", r"move.*file"
            ],
            TaskType.ANALYSIS: [
                r"analyze.*code", r"review.*code", r"check.*syntax", r"find.*bugs",
                r"explain.*code", r"understand.*function", r"debug.*issue"
            ],
            TaskType.RESEARCH: [
                r"search.*for", r"find.*information", r"look.*up", r"research.*topic",
                r"get.*documentation", r"learn.*about"
            ],
            TaskType.EXECUTION: [
                r"run.*code", r"execute.*script", r"test.*function", r"compile.*program",
                r"start.*server", r"launch.*application"
            ]
        }
        
        self.logger.info("TaskPlanner initialized")
    
    async def plan_tasks(self, user_request: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
        """
        Plan tasks for a user request.
        
        Args:
            user_request: User's request description
            context: Additional context information
            
        Returns:
            List of planned tasks
        """
        self.logger.info(f"Planning tasks for request: {user_request}")
        
        # Use AI to decompose the task
        tasks = await self._ai_task_decomposition(user_request, context)
        
        # Enhance tasks with tool requirements and dependencies
        enhanced_tasks = self._enhance_tasks(tasks)
        
        # Optimize task order
        ordered_tasks = self._optimize_task_order(enhanced_tasks)
        
        self.logger.info(f"Planned {len(ordered_tasks)} tasks")
        return ordered_tasks
    
    async def _ai_task_decomposition(self, request: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
        """Use AI to decompose the request into tasks."""
        try:
            model_manager = get_model_manager()
            
            # Create decomposition prompt
            prompt = self._create_decomposition_prompt(request, context)
            
            # Get AI response
            response = await model_manager.generate(
                prompt=prompt,
                max_tokens=1000,
                temperature=0.3
            )
            
            # Parse AI response into tasks
            tasks = self._parse_ai_response(response)
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"AI task decomposition failed: {e}")
            # Fallback to pattern-based decomposition
            return self._pattern_based_decomposition(request)
    
    def _create_decomposition_prompt(self, request: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Create prompt for AI task decomposition."""
        prompt = f"""
You are a task planning AI. Break down the following user request into specific, actionable tasks.

User Request: {request}

Available Tools:
- read_file: Read file contents
- write_file: Write content to a file
- list_directory: List directory contents
- delete_file: Delete files or directories
- execute_python: Execute Python code
- execute_shell: Execute shell commands

Task Types:
- code_generation: Writing or creating code
- file_operation: File system operations
- analysis: Code analysis or review
- research: Information gathering
- execution: Running code or commands
- communication: Providing responses or explanations

Please provide a numbered list of tasks in this format:
1. [TASK_TYPE] Task description
2. [TASK_TYPE] Task description
...

Example:
1. [file_operation] Create a new Python file called 'calculator.py'
2. [code_generation] Write a calculator function with basic operations
3. [execution] Test the calculator function with sample inputs
4. [communication] Explain how the calculator works

Tasks:
"""
        
        if context:
            prompt += f"\nContext: {context}\n"
        
        return prompt
    
    def _parse_ai_response(self, response: str) -> List[Task]:
        """Parse AI response into Task objects."""
        tasks = []
        lines = response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or not re.match(r'^\d+\.', line):
                continue
            
            # Extract task type and description
            match = re.match(r'^\d+\.\s*\[(\w+)\]\s*(.+)', line)
            if match:
                task_type_str, description = match.groups()
                
                try:
                    task_type = TaskType(task_type_str.lower())
                except ValueError:
                    task_type = TaskType.COMMUNICATION
                
                # Generate task ID
                import uuid
                task_id = str(uuid.uuid4())
                
                # Determine required tools
                tools_required = self._determine_required_tools(description, task_type)
                
                task = Task(
                    id=task_id,
                    description=description,
                    task_type=task_type,
                    tools_required=tools_required,
                    estimated_complexity=self._estimate_complexity(description, task_type)
                )
                
                tasks.append(task)
        
        return tasks
    
    def _pattern_based_decomposition(self, request: str) -> List[Task]:
        """Fallback pattern-based task decomposition."""
        tasks = []
        request_lower = request.lower()
        
        # Detect task types based on patterns
        detected_types = []
        for task_type, patterns in self.task_patterns.items():
            for pattern in patterns:
                if re.search(pattern, request_lower):
                    detected_types.append(task_type)
                    break
        
        # Create basic tasks
        if not detected_types:
            detected_types = [TaskType.COMMUNICATION]
        
        for i, task_type in enumerate(detected_types):
            import uuid
            task_id = str(uuid.uuid4())
            
            task = Task(
                id=task_id,
                description=f"Execute {task_type.value} for: {request}",
                task_type=task_type,
                tools_required=self._determine_required_tools(request, task_type),
                estimated_complexity=0.5
            )
            
            tasks.append(task)
        
        return tasks
    
    def _determine_required_tools(self, description: str, task_type: TaskType) -> List[str]:
        """Determine required tools for a task."""
        tools = []
        description_lower = description.lower()
        
        # File operation tools
        if any(word in description_lower for word in ['file', 'read', 'write', 'create', 'delete']):
            if 'read' in description_lower:
                tools.append('read_file')
            if any(word in description_lower for word in ['write', 'create', 'save']):
                tools.append('write_file')
            if 'list' in description_lower or 'directory' in description_lower:
                tools.append('list_directory')
            if 'delete' in description_lower:
                tools.append('delete_file')
        
        # Code execution tools
        if any(word in description_lower for word in ['run', 'execute', 'test']):
            if 'python' in description_lower:
                tools.append('execute_python')
            if any(word in description_lower for word in ['shell', 'command', 'bash']):
                tools.append('execute_shell')
        
        # Task type specific tools
        if task_type == TaskType.CODE_GENERATION:
            tools.append('write_file')
        elif task_type == TaskType.FILE_OPERATION:
            if not tools:  # If no specific file tools detected
                tools.extend(['read_file', 'write_file'])
        elif task_type == TaskType.EXECUTION:
            tools.append('execute_python')
        
        return list(set(tools))  # Remove duplicates
    
    def _estimate_complexity(self, description: str, task_type: TaskType) -> float:
        """Estimate task complexity (0.0 to 1.0)."""
        base_complexity = {
            TaskType.COMMUNICATION: 0.2,
            TaskType.FILE_OPERATION: 0.3,
            TaskType.ANALYSIS: 0.5,
            TaskType.CODE_GENERATION: 0.7,
            TaskType.EXECUTION: 0.4,
            TaskType.RESEARCH: 0.6
        }
        
        complexity = base_complexity.get(task_type, 0.5)
        
        # Adjust based on description complexity
        description_lower = description.lower()
        
        # Increase complexity for certain keywords
        if any(word in description_lower for word in ['complex', 'advanced', 'algorithm', 'optimize']):
            complexity += 0.2
        if any(word in description_lower for word in ['multiple', 'several', 'many']):
            complexity += 0.1
        
        # Decrease complexity for simple tasks
        if any(word in description_lower for word in ['simple', 'basic', 'easy']):
            complexity -= 0.2
        
        return max(0.1, min(1.0, complexity))
    
    def _enhance_tasks(self, tasks: List[Task]) -> List[Task]:
        """Enhance tasks with dependencies and additional metadata."""
        # Add dependencies based on task types and content
        for i, task in enumerate(tasks):
            # File creation should come before file operations
            if task.task_type == TaskType.FILE_OPERATION and 'read' in task.description.lower():
                for j, prev_task in enumerate(tasks[:i]):
                    if (prev_task.task_type == TaskType.CODE_GENERATION or 
                        ('create' in prev_task.description.lower() and 'file' in prev_task.description.lower())):
                        task.dependencies.append(prev_task.id)
            
            # Execution should come after code generation
            if task.task_type == TaskType.EXECUTION:
                for j, prev_task in enumerate(tasks[:i]):
                    if prev_task.task_type == TaskType.CODE_GENERATION:
                        task.dependencies.append(prev_task.id)
        
        return tasks
    
    def _optimize_task_order(self, tasks: List[Task]) -> List[Task]:
        """Optimize task execution order based on dependencies and complexity."""
        # Topological sort based on dependencies
        ordered_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # Find tasks with no unresolved dependencies
            ready_tasks = []
            for task in remaining_tasks:
                if all(dep_id in [t.id for t in ordered_tasks] for dep_id in task.dependencies):
                    ready_tasks.append(task)
            
            if not ready_tasks:
                # No tasks ready, add remaining tasks anyway (circular dependency or error)
                ready_tasks = remaining_tasks
            
            # Sort ready tasks by complexity (simple first)
            ready_tasks.sort(key=lambda t: t.estimated_complexity)
            
            # Add the first ready task
            next_task = ready_tasks[0]
            ordered_tasks.append(next_task)
            remaining_tasks.remove(next_task)
        
        return ordered_tasks
