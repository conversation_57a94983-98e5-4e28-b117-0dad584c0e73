"""
Reverie CLI - AI-native code development server with Augment-like capabilities.

This package provides a comprehensive AI-powered development environment that combines:
- FastAPI web server with REST API endpoints
- Interactive CLI with AI coding assistant capabilities  
- Multi-backend AI model management (Transformers, vLLM, GGUF, TensorFlow)
- Intelligent agent system for code analysis and generation
- Tool integration for file operations, web search, and project management
- Modern web interface for monitoring and interaction

Key Features:
- Dual-mode operation: Server API + AI Coder
- Support for Lucy-128k and other popular models
- Real-time interactive console with command support
- Extensible plugin system
- Advanced logging and monitoring
"""

__version__ = "0.1.0"
__author__ = "Rilance Code Studio"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Core imports for easy access
from reverie_cli.core.config import Settings, get_settings
from reverie_cli.core.logging import setup_logging, get_logger

# Version info
VERSION_INFO = {
    "version": __version__,
    "author": __author__,
    "license": __license__,
    "description": "AI-native code development server with Augment-like capabilities",
}

# Initialize logging on import
logger = get_logger(__name__)
logger.info(f"Reverie CLI v{__version__} initialized")

__all__ = [
    "__version__",
    "__author__", 
    "__email__",
    "__license__",
    "VERSION_INFO",
    "Settings",
    "get_settings",
    "setup_logging",
    "get_logger",
]
