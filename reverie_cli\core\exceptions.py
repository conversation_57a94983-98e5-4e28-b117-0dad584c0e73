"""
Exception handling for Reverie CLI.

This module defines custom exceptions used throughout the application
with proper error codes, messages, and context information.
"""

from typing import Optional, Dict, Any


class ReverieError(Exception):
    """Base exception for all Reverie CLI errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}
        super().__init__(self.message)
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.error_code,
            "message": self.message,
            "context": self.context
        }


class ConfigurationError(ReverieError):
    """Raised when there's a configuration error."""
    pass


class ModelError(ReverieError):
    """Base exception for model-related errors."""
    pass


class ModelNotFoundError(ModelError):
    """Raised when a requested model is not found."""
    pass


class ModelLoadError(ModelError):
    """Raised when a model fails to load."""
    pass


class ModelInferenceError(ModelError):
    """Raised when model inference fails."""
    pass


class AgentError(ReverieError):
    """Base exception for agent-related errors."""
    pass


class AgentTimeoutError(AgentError):
    """Raised when an agent operation times out."""
    pass


class AgentToolError(AgentError):
    """Raised when an agent tool fails."""
    pass


class AgentMaxIterationsError(AgentError):
    """Raised when an agent exceeds maximum iterations."""
    pass


class ToolError(ReverieError):
    """Base exception for tool-related errors."""
    pass


class ToolNotFoundError(ToolError):
    """Raised when a requested tool is not found."""
    pass


class ToolExecutionError(ToolError):
    """Raised when a tool execution fails."""
    pass


class FileOperationError(ReverieError):
    """Raised when file operations fail."""
    pass


class NetworkError(ReverieError):
    """Raised when network operations fail."""
    pass


class AuthenticationError(ReverieError):
    """Raised when authentication fails."""
    pass


class ValidationError(ReverieError):
    """Raised when input validation fails."""
    pass


class DatabaseError(ReverieError):
    """Raised when database operations fail."""
    pass


class PluginError(ReverieError):
    """Base exception for plugin-related errors."""
    pass


class PluginLoadError(PluginError):
    """Raised when a plugin fails to load."""
    pass


class PluginNotFoundError(PluginError):
    """Raised when a requested plugin is not found."""
    pass


# Error code mappings for HTTP status codes
ERROR_CODE_TO_HTTP_STATUS = {
    "ReverieError": 500,
    "ConfigurationError": 500,
    "ModelError": 500,
    "ModelNotFoundError": 404,
    "ModelLoadError": 500,
    "ModelInferenceError": 500,
    "AgentError": 500,
    "AgentTimeoutError": 408,
    "AgentToolError": 500,
    "AgentMaxIterationsError": 429,
    "ToolError": 500,
    "ToolNotFoundError": 404,
    "ToolExecutionError": 500,
    "FileOperationError": 500,
    "NetworkError": 502,
    "AuthenticationError": 401,
    "ValidationError": 400,
    "DatabaseError": 500,
    "PluginError": 500,
    "PluginLoadError": 500,
    "PluginNotFoundError": 404,
}


def get_http_status_for_error(error: ReverieError) -> int:
    """Get HTTP status code for a Reverie error."""
    return ERROR_CODE_TO_HTTP_STATUS.get(error.error_code, 500)
