"""
Model information and metadata handling.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

from pydantic import BaseModel, Field


class ModelStatus(str, Enum):
    """Model status enumeration."""
    AVAILABLE = "available"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"
    UNLOADING = "unloading"


class ModelBackend(str, Enum):
    """Model backend enumeration."""
    AUTO = "auto"
    TRANSFORMERS = "transformers"
    VLLM = "vllm"
    GGUF = "gguf"
    TENSORFLOW = "tensorflow"


@dataclass
class ModelInfo:
    """Model information dataclass."""
    name: str
    backend: ModelBackend
    status: ModelStatus
    size_gb: Optional[float] = None
    context_length: Optional[int] = None
    description: Optional[str] = None
    loaded_at: Optional[datetime] = None
    device: Optional[str] = None
    memory_usage_gb: Optional[float] = None
    parameters: Optional[int] = None
    architecture: Optional[str] = None
    license: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "backend": self.backend.value,
            "status": self.status.value,
            "size_gb": self.size_gb,
            "context_length": self.context_length,
            "description": self.description,
            "loaded_at": self.loaded_at.isoformat() if self.loaded_at else None,
            "device": self.device,
            "memory_usage_gb": self.memory_usage_gb,
            "parameters": self.parameters,
            "architecture": self.architecture,
            "license": self.license,
            "tags": self.tags or [],
            "metadata": self.metadata or {}
        }


# Predefined popular models
POPULAR_MODELS = {
    "Menlo/Lucy-128k-gguf": ModelInfo(
        name="Menlo/Lucy-128k-gguf",
        backend=ModelBackend.GGUF,
        status=ModelStatus.AVAILABLE,
        size_gb=7.2,
        context_length=131072,
        description="Lucy-128k model with 128k context length (default)",
        parameters=7_000_000_000,
        architecture="Llama",
        license="Apache-2.0",
        tags=["chat", "code", "long-context"]
    ),
    "meta-llama/Llama-2-7b-chat-hf": ModelInfo(
        name="meta-llama/Llama-2-7b-chat-hf",
        backend=ModelBackend.TRANSFORMERS,
        status=ModelStatus.AVAILABLE,
        size_gb=13.5,
        context_length=4096,
        description="Llama 2 7B Chat model",
        parameters=7_000_000_000,
        architecture="Llama",
        license="Custom",
        tags=["chat", "general"]
    ),
    "mistralai/Mistral-7B-Instruct-v0.1": ModelInfo(
        name="mistralai/Mistral-7B-Instruct-v0.1",
        backend=ModelBackend.TRANSFORMERS,
        status=ModelStatus.AVAILABLE,
        size_gb=14.2,
        context_length=8192,
        description="Mistral 7B Instruct model",
        parameters=7_000_000_000,
        architecture="Mistral",
        license="Apache-2.0",
        tags=["chat", "instruct"]
    ),
    "codellama/CodeLlama-7b-Python-hf": ModelInfo(
        name="codellama/CodeLlama-7b-Python-hf",
        backend=ModelBackend.TRANSFORMERS,
        status=ModelStatus.AVAILABLE,
        size_gb=13.1,
        context_length=16384,
        description="Code Llama 7B Python specialized model",
        parameters=7_000_000_000,
        architecture="Llama",
        license="Custom",
        tags=["code", "python"]
    ),
    "microsoft/phi-3-mini-4k-instruct": ModelInfo(
        name="microsoft/phi-3-mini-4k-instruct",
        backend=ModelBackend.TRANSFORMERS,
        status=ModelStatus.AVAILABLE,
        size_gb=7.6,
        context_length=4096,
        description="Phi-3 Mini 4K Instruct model",
        parameters=3_800_000_000,
        architecture="Phi",
        license="MIT",
        tags=["chat", "small", "efficient"]
    ),
    "google/gemma-7b-it": ModelInfo(
        name="google/gemma-7b-it",
        backend=ModelBackend.TRANSFORMERS,
        status=ModelStatus.AVAILABLE,
        size_gb=14.8,
        context_length=8192,
        description="Gemma 7B Instruction Tuned model",
        parameters=7_000_000_000,
        architecture="Gemma",
        license="Apache-2.0",
        tags=["chat", "instruct", "google"]
    )
}


def get_model_info(model_name: str) -> Optional[ModelInfo]:
    """Get model information by name."""
    return POPULAR_MODELS.get(model_name)


def list_available_models() -> List[ModelInfo]:
    """List all available models."""
    return list(POPULAR_MODELS.values())


def search_models(query: str) -> List[ModelInfo]:
    """Search models by name or tags."""
    query = query.lower()
    results = []
    
    for model in POPULAR_MODELS.values():
        # Search in name
        if query in model.name.lower():
            results.append(model)
            continue
            
        # Search in description
        if model.description and query in model.description.lower():
            results.append(model)
            continue
            
        # Search in tags
        if model.tags and any(query in tag.lower() for tag in model.tags):
            results.append(model)
            continue
    
    return results
