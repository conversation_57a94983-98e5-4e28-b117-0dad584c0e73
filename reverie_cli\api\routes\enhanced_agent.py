"""
Enhanced Agent API routes for Reverie CLI.

This module provides advanced agent endpoints with integrated AI engines
for comprehensive development assistance similar to Augment.
"""

from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
import asyncio

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ReverieError
from reverie_cli.api.dependencies import get_model_manager_dep


router = APIRouter()
logger = get_logger("enhanced_agent")


class AgentTask(BaseModel):
    """Enhanced agent task model."""
    task_type: str = Field(..., description="Type of task (code, analyze, search, debug, etc.)")
    description: str = Field(..., description="Task description")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    project_path: Optional[str] = Field(None, description="Project path for context")
    use_engines: List[str] = Field(["web", "context", "memory"], description="Engines to use")
    priority: str = Field("normal", description="Task priority (low, normal, high, urgent)")
    remember_result: bool = Field(True, description="Remember task result for learning")


class AgentResponse(BaseModel):
    """Enhanced agent response model."""
    task_id: str = Field(..., description="Unique task ID")
    status: str = Field(..., description="Task status")
    result: Dict[str, Any] = Field(..., description="Task result")
    engines_used: List[str] = Field(..., description="Engines used for the task")
    execution_time: float = Field(..., description="Execution time in seconds")
    suggestions: Optional[List[str]] = Field(None, description="Follow-up suggestions")


@router.post("/agent/execute", response_model=AgentResponse)
async def execute_agent_task(
    task: AgentTask,
    background_tasks: BackgroundTasks,
    model_manager=Depends(get_model_manager_dep)
):
    """
    Execute an enhanced agent task with AI engine integration.
    
    Supports various task types:
    - code: Code generation, editing, analysis
    - analyze: Project and code analysis
    - search: Intelligent search across web and memory
    - debug: AI-powered debugging assistance
    - review: Code review and suggestions
    - optimize: Performance optimization
    - security: Security analysis and fixes
    """
    import time
    import uuid
    
    start_time = time.time()
    task_id = f"task-{uuid.uuid4().hex[:8]}"
    
    try:
        logger.info(f"Executing agent task {task_id}: {task.task_type}")
        
        # Initialize requested engines
        engines = await _init_requested_engines(task.use_engines)
        engines_used = list(engines.keys())
        
        # Execute task based on type
        if task.task_type == "code":
            result = await _execute_code_task(task, engines, model_manager)
        elif task.task_type == "analyze":
            result = await _execute_analyze_task(task, engines)
        elif task.task_type == "search":
            result = await _execute_search_task(task, engines)
        elif task.task_type == "debug":
            result = await _execute_debug_task(task, engines, model_manager)
        elif task.task_type == "review":
            result = await _execute_review_task(task, engines, model_manager)
        elif task.task_type == "optimize":
            result = await _execute_optimize_task(task, engines, model_manager)
        elif task.task_type == "security":
            result = await _execute_security_task(task, engines, model_manager)
        else:
            result = await _execute_general_task(task, engines, model_manager)
        
        # Generate follow-up suggestions
        suggestions = await _generate_suggestions(task, result, engines)
        
        # Remember task if requested
        if task.remember_result and engines.get("memory"):
            background_tasks.add_task(
                _remember_agent_task,
                task,
                result,
                engines["memory"]
            )
        
        execution_time = time.time() - start_time
        
        return AgentResponse(
            task_id=task_id,
            status="completed",
            result=result,
            engines_used=engines_used,
            execution_time=execution_time,
            suggestions=suggestions
        )
        
    except Exception as e:
        logger.error(f"Agent task {task_id} failed: {e}")
        execution_time = time.time() - start_time
        
        return AgentResponse(
            task_id=task_id,
            status="failed",
            result={"error": str(e)},
            engines_used=engines_used if 'engines_used' in locals() else [],
            execution_time=execution_time,
            suggestions=["Check the error message and try again with different parameters"]
        )


@router.post("/agent/smart-assist")
async def smart_assistance(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    model_manager=Depends(get_model_manager_dep)
):
    """
    Provide smart AI assistance with automatic engine selection.
    
    Automatically determines the best approach and engines to use
    based on the user's request and context.
    """
    try:
        user_request = request.get("request", "")
        context = request.get("context", {})
        
        if not user_request:
            raise HTTPException(status_code=400, detail="Request description required")
        
        # Analyze request to determine optimal approach
        task_analysis = await _analyze_user_request(user_request, context)
        
        # Initialize optimal engines
        engines = await _init_requested_engines(task_analysis["recommended_engines"])
        
        # Execute smart assistance
        result = await _execute_smart_assistance(
            user_request,
            context,
            task_analysis,
            engines,
            model_manager
        )
        
        # Learn from this interaction
        if engines.get("memory"):
            background_tasks.add_task(
                _learn_from_smart_assist,
                user_request,
                context,
                result,
                engines["memory"]
            )
        
        return {
            "response": result["response"],
            "approach": task_analysis["approach"],
            "engines_used": list(engines.keys()),
            "confidence": task_analysis["confidence"],
            "follow_up_suggestions": result.get("suggestions", [])
        }
        
    except Exception as e:
        logger.error(f"Smart assistance failed: {e}")
        raise HTTPException(status_code=500, detail="Smart assistance failed")


@router.get("/agent/capabilities")
async def get_agent_capabilities():
    """Get current agent capabilities and available engines."""
    try:
        # Check available engines
        engines = await _init_requested_engines(["web", "context", "memory"])
        
        capabilities = {
            "task_types": [
                "code", "analyze", "search", "debug", "review", 
                "optimize", "security", "general"
            ],
            "available_engines": list(engines.keys()),
            "features": {
                "web_search": "web" in engines,
                "code_analysis": "context" in engines,
                "memory_learning": "memory" in engines,
                "smart_assistance": True,
                "multi_engine_integration": True,
                "background_learning": True
            },
            "supported_languages": [
                "python", "javascript", "typescript", "java", "cpp", 
                "go", "rust", "ruby", "php", "csharp"
            ],
            "analysis_types": [
                "quick", "comprehensive", "security", "performance"
            ]
        }
        
        return capabilities
        
    except Exception as e:
        logger.error(f"Failed to get capabilities: {e}")
        raise HTTPException(status_code=500, detail="Failed to get capabilities")


# Helper functions

async def _init_requested_engines(engine_names: List[str]) -> Dict[str, Any]:
    """Initialize requested engines."""
    engines = {}
    
    for engine_name in engine_names:
        try:
            if engine_name == "web":
                from reverie_cli.tools.web_engine import WebEngine
                engines["web"] = WebEngine()
            elif engine_name == "context":
                from reverie_cli.tools.context_engine import ContextEngine
                engines["context"] = ContextEngine()
            elif engine_name == "memory":
                from reverie_cli.tools.memory_engine import MemoryEngine
                engines["memory"] = MemoryEngine()
        except Exception as e:
            logger.warning(f"Failed to initialize {engine_name} engine: {e}")
    
    return engines


async def _analyze_user_request(request: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze user request to determine optimal approach."""
    request_lower = request.lower()
    
    # Determine task type
    if any(keyword in request_lower for keyword in ["code", "function", "class", "implement", "write"]):
        task_type = "code"
        recommended_engines = ["context", "memory", "web"]
    elif any(keyword in request_lower for keyword in ["analyze", "understand", "explain", "structure"]):
        task_type = "analyze"
        recommended_engines = ["context", "memory"]
    elif any(keyword in request_lower for keyword in ["search", "find", "look for", "what is"]):
        task_type = "search"
        recommended_engines = ["web", "memory"]
    elif any(keyword in request_lower for keyword in ["debug", "error", "bug", "fix", "issue"]):
        task_type = "debug"
        recommended_engines = ["web", "context", "memory"]
    elif any(keyword in request_lower for keyword in ["review", "check", "improve", "optimize"]):
        task_type = "review"
        recommended_engines = ["context", "memory"]
    else:
        task_type = "general"
        recommended_engines = ["web", "context", "memory"]
    
    return {
        "task_type": task_type,
        "approach": f"Smart {task_type} assistance with AI engine integration",
        "recommended_engines": recommended_engines,
        "confidence": 0.8
    }


async def _execute_smart_assistance(
    request: str,
    context: Dict[str, Any],
    analysis: Dict[str, Any],
    engines: Dict[str, Any],
    model_manager
) -> Dict[str, Any]:
    """Execute smart assistance based on analysis."""
    
    # Build comprehensive context
    context_parts = [f"User Request: {request}"]
    
    # Add relevant information from engines
    if engines.get("web") and analysis["task_type"] in ["search", "debug", "code"]:
        try:
            web_result = await engines["web"].smart_search(request, max_results=3)
            if web_result.success:
                context_parts.append(f"Web Research: {web_result.data}")
        except Exception as e:
            logger.warning(f"Web search failed: {e}")
    
    if engines.get("memory"):
        try:
            memory_result = await engines["memory"].intelligent_search(request, max_results=3)
            if memory_result.success:
                context_parts.append(f"Relevant Experience: {memory_result.data}")
        except Exception as e:
            logger.warning(f"Memory search failed: {e}")
    
    if engines.get("context") and context.get("project_path"):
        try:
            context_result = await engines["context"].smart_analyze(
                context["project_path"], analysis_type="quick"
            )
            if context_result.success:
                context_parts.append(f"Project Context: {context_result.data}")
        except Exception as e:
            logger.warning(f"Context analysis failed: {e}")
    
    # Generate intelligent response
    model = model_manager.get_current_model()
    if not model:
        raise HTTPException(status_code=503, detail="No model loaded")
    
    smart_prompt = f"""You are an expert AI assistant with access to enhanced capabilities.

Context Information:
{chr(10).join(context_parts)}

Task Type: {analysis['task_type']}
Approach: {analysis['approach']}

Please provide comprehensive assistance that:
1. Directly addresses the user's request
2. Uses the provided context effectively
3. Offers practical, actionable solutions
4. Includes relevant examples when helpful
5. Suggests logical next steps

Focus on being helpful, accurate, and thorough in your response."""
    
    messages = [
        {"role": "system", "content": smart_prompt},
        {"role": "user", "content": request}
    ]
    
    response = await model.generate_response(messages=messages)
    
    return {
        "response": response.get("content", "I apologize, but I couldn't generate a response."),
        "suggestions": [
            "Ask follow-up questions for clarification",
            "Request specific examples or implementations",
            "Explore related topics or alternatives"
        ]
    }
