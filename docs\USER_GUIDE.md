# 📖 Reverie CLI - 用户指南

## 🎯 概述

Reverie CLI 是一个AI-Native开发服务器，具有Augment-like功能，支持双模式操作：AI编程助手和API服务。本指南将详细介绍如何使用所有功能。

## 🚀 快速开始

### 环境要求
- **Windows 10/11** (支持UTF-8编码)
- **Python 3.8+** (推荐Python 3.9+)
- **CUDA 12.8** (可选，用于GPU加速)
- **4GB+ RAM** (推荐8GB+)
- **2GB+ 磁盘空间**

### 安装步骤

#### 1. 环境设置
```bash
# 运行环境设置脚本
setup.bat
```

这将自动：
- 检查Python环境
- 创建虚拟环境 `reverie_env`
- 安装PyTorch (CUDA 12.8版本)
- 安装所有依赖包
- 配置项目环境

#### 2. 启动应用
```bash
# 交互式启动
start.bat

# 或直接指定模式
start.bat --server    # 仅服务器
start.bat --console   # 仅控制台
start.bat --dual      # 双模式
```

## 🎪 双模式操作

### 🤖 AI Coder Mode
自然语言编程助手，支持对话式编程。

#### 基本AI命令
```bash
# 直接AI助手
ai "创建一个FastAPI用户认证系统"
ai "解释这段代码的作用"
ai "优化这个函数的性能"

# 代码生成和编辑
code create auth.py "用户认证模块，包含JWT token生成"
code edit main.py "添加错误处理和日志记录"
code analyze ./src "分析代码结构和质量"
code review ./src/auth.py "代码审查和改进建议"
code test ./src "生成单元测试"
```

#### 项目分析
```bash
# 项目级别分析
analyze ./my-project
analyze ./src --type comprehensive
analyze ./api --focus security

# 代码审查
review ./src
review ./src/main.py --style pep8
review ./api --focus performance
```

### ⚙️ API Service Mode
服务器管理和系统监控功能。

#### 系统管理
```bash
# 系统状态
status                    # 显示系统状态
status --detailed         # 详细系统信息

# 模型管理
models                    # 列出可用模型
models list --remote      # 列出远程可用模型
models load lucy-128k     # 加载特定模型
models unload            # 卸载当前模型
models info              # 显示当前模型信息

# 配置管理
config show              # 显示当前配置
config set temperature 0.7  # 设置参数
config set max_tokens 4096  # 设置最大token数
config reset             # 重置为默认配置
```

## 🌐 Enhanced Engines 使用指南

### Web Engine - 智能网络搜索

#### 基本搜索
```bash
# 智能搜索
web "FastAPI最佳实践 2024"
web "Python异步编程教程"
web "JWT认证安全性"

# 指定搜索类型
web search "机器学习算法" --type academic
web search "API设计模式" --type tutorial
web search "数据库优化" --type documentation
```

#### 内容提取
```bash
# 提取网页内容
web extract https://fastapi.tiangolo.com/
web extract https://docs.python.org/3/library/asyncio.html
web extract https://jwt.io/introduction/ --format markdown

# 批量提取
web extract-batch urls.txt --output ./extracted/
```

#### 网站监控
```bash
# 监控网站变化
web monitor https://github.com/microsoft/vscode/releases
web monitor https://pypi.org/project/fastapi/ --interval 1h
web monitor https://news.ycombinator.com --keywords "AI,Python"

# 查看监控状态
web monitor list
web monitor stop https://example.com
```

#### 高级搜索功能
```bash
# 多引擎搜索
web search "Python web框架比较" --engines google,bing,duckduckgo
web search "API安全最佳实践" --engines all

# 时间范围搜索
web search "AI编程助手" --timeframe "past_month"
web search "FastAPI新功能" --timeframe "past_week"

# 结果过滤
web search "Python教程" --filter tutorial --min-quality 0.8
web search "机器学习" --exclude "广告,推广" --language zh
```

### Context Engine - 代码分析

#### 项目分析
```bash
# 快速分析
context analyze ./project
context analyze ./src --type quick

# 全面分析
context analyze ./project --type comprehensive
context analyze ./api --include-dependencies
context analyze ./src --include-metrics

# 特定语言分析
context analyze ./frontend --language javascript
context analyze ./backend --language python
context analyze ./mobile --language dart
```

#### 代码质量评估
```bash
# 质量检查
context quality ./src
context quality ./api --focus complexity
context quality ./utils --focus maintainability

# 安全分析
context security ./src
context security ./api --level comprehensive
context security ./auth --focus vulnerabilities

# 性能分析
context performance ./src
context performance ./api --focus bottlenecks
context performance ./db --focus queries
```

#### 依赖分析
```bash
# 依赖关系
context dependencies ./project
context dependencies ./src --show-graph
context dependencies ./api --check-vulnerabilities

# 架构分析
context architecture ./project
context architecture ./src --show-patterns
context architecture ./api --check-design
```

### Memory Engine - 智能记忆

#### 存储信息
```bash
# 基本存储
remember "项目使用FastAPI框架" project
remember "数据库使用PostgreSQL" database
remember "认证使用JWT token" security

# 带标签存储
remember "用户偏好RESTful API设计" preferences api design
remember "团队使用Git Flow工作流" workflow git team
remember "部署使用Docker容器" deployment docker

# 代码片段存储
remember "
def create_jwt_token(user_id):
    payload = {'user_id': user_id, 'exp': datetime.utcnow() + timedelta(hours=24)}
    return jwt.encode(payload, SECRET_KEY, algorithm='HS256')
" code jwt python
```

#### 搜索记忆
```bash
# 基本搜索
search "FastAPI"
search "JWT认证"
search "数据库设计"

# 高级搜索
search "API设计" --type semantic
search "认证" --tags security auth
search "部署" --timeframe "past_month"

# 模糊搜索
search "用户管理" --fuzzy
search "数据库" --similar
search "API" --related
```

#### 记忆管理
```bash
# 查看记忆统计
memory stats
memory stats --by-tag
memory stats --by-type

# 记忆清理
memory clean --older-than 30d
memory clean --low-importance
memory clean --tag deprecated

# 记忆导出/导入
memory export ./backup/memory.json
memory import ./backup/memory.json
```

## 🎪 一行复杂操作

### 智能解决方案
```bash
# 组合AI + Web + Memory的智能解决方案
smart_solution "如何实现用户认证系统"
smart_solution "API性能优化策略"
smart_solution "数据库设计最佳实践"

# 指定解决方案类型
smart_solution "微服务架构设计" --type architecture
smart_solution "安全漏洞修复" --type security
smart_solution "性能瓶颈分析" --type performance
```

### 项目健康检查
```bash
# 全面项目健康检查
project_health "./my-api"
project_health "./frontend" --include-dependencies
project_health "./backend" --focus security

# 生成健康报告
project_health "./project" --report ./reports/health.html
project_health "./src" --format json --output ./health.json
```

### 全面代码分析
```bash
# 代码 + 质量 + 安全分析
full_analysis "./src"
full_analysis "./api" --include-performance
full_analysis "./project" --generate-report

# 指定分析深度
full_analysis "./src" --depth comprehensive
full_analysis "./api" --depth quick
full_analysis "./utils" --depth security-focused
```

## 🌐 Web界面使用

### 访问地址
当服务器运行时，可通过以下地址访问：

- **主页面**: http://localhost:8000/
- **API文档**: http://localhost:8000/docs
- **聊天界面**: http://localhost:8000/chat
- **系统状态**: http://localhost:8000/health
- **增强API**: http://localhost:8000/v1/chat/enhanced

### 聊天界面功能
1. **实时对话**: 与AI进行自然语言对话
2. **代码高亮**: 自动识别和高亮代码
3. **文件上传**: 上传代码文件进行分析
4. **历史记录**: 保存对话历史
5. **导出功能**: 导出对话内容

### API文档使用
1. **交互式测试**: 直接在文档中测试API
2. **参数说明**: 详细的参数和返回值说明
3. **示例代码**: 多种语言的调用示例
4. **认证测试**: 支持API密钥认证测试

## 🔧 高级配置

### 环境变量配置
```bash
# 在控制台中设置
config set OPENAI_API_KEY "your-api-key"
config set ANTHROPIC_API_KEY "your-anthropic-key"
config set HUGGINGFACE_TOKEN "your-hf-token"

# 服务器配置
config set SERVER_HOST "0.0.0.0"
config set SERVER_PORT "8000"
config set SERVER_WORKERS "4"

# 模型配置
config set DEFAULT_MODEL "lucy-128k"
config set MAX_CONTEXT_LENGTH "131072"
config set TEMPERATURE "0.7"
```

### 自定义工具配置
```bash
# Web引擎配置
config set WEB_SEARCH_ENGINES "google,bing,duckduckgo"
config set WEB_CACHE_TTL "3600"
config set WEB_MAX_RESULTS "10"

# Context引擎配置
config set CONTEXT_ANALYSIS_DEPTH "comprehensive"
config set CONTEXT_CACHE_RESULTS "true"
config set CONTEXT_INCLUDE_DEPENDENCIES "true"

# Memory引擎配置
config set MEMORY_MAX_ITEMS "100000"
config set MEMORY_LEARNING_RATE "0.1"
config set MEMORY_AUTO_CLEANUP "true"
```

## 🚨 故障排除

### 常见问题

#### 1. 环境问题
```bash
# Python版本检查
python --version

# 虚拟环境问题
setup.bat  # 重新设置环境

# 依赖问题
pip install -r requirements.txt --force-reinstall
```

#### 2. 模型加载问题
```bash
# 检查模型状态
models info

# 重新加载模型
models unload
models load lucy-128k

# 检查GPU支持
python -c "import torch; print(torch.cuda.is_available())"
```

#### 3. 网络连接问题
```bash
# 测试网络连接
web search "test" --engines google

# 检查代理设置
config show | findstr proxy

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 日志和调试
```bash
# 查看日志
type logs\reverie_cli.log

# 启用调试模式
config set LOG_LEVEL "DEBUG"

# 清理日志
del logs\*.log
```

## 📊 性能优化

### 系统优化
```bash
# 检查系统资源
status --detailed

# 优化内存使用
config set MEMORY_OPTIMIZATION "true"
config set CACHE_SIZE "1000"

# GPU加速
config set USE_GPU "true"
config set GPU_MEMORY_FRACTION "0.8"
```

### 模型优化
```bash
# 使用量化模型
models load lucy-128k-quantized

# 调整上下文长度
config set MAX_CONTEXT_LENGTH "65536"

# 批处理优化
config set BATCH_SIZE "4"
```

这个用户指南涵盖了Reverie CLI的所有主要功能。接下来我将创建更多专门的文档。
