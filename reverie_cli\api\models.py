"""
Pydantic models for API requests and responses.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model."""
    success: bool = True
    message: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    context: Optional[Dict[str, Any]] = None


# Re-export models from route modules for convenience
__all__ = [
    "BaseResponse",
    "ErrorResponse",
]
