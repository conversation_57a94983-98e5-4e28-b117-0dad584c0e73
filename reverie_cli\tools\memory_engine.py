"""
Enhanced Memory & Search Engine for Reverie CLI.

This module provides advanced memory management and search capabilities
similar to Augment's memory system. It includes:
- Persistent memory storage with intelligent indexing
- Semantic search across conversations, code, and web content
- Learning from user interactions and patterns
- Context-aware memory retrieval and suggestions
- One-line function calls for complex memory operations
- Real-time memory updates and synchronization
"""

import asyncio
import json
import sqlite3
import hashlib
import re
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import pickle
import base64

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings
from reverie_cli.tools.base import BaseTool, ToolResult, ToolError, ToolCategory, ToolParameter


@dataclass
class MemoryItem:
    """Represents a memory item with metadata and content."""
    id: str
    type: str  # conversation, code, web, insight, pattern, etc.
    content: str
    metadata: Dict[str, Any]
    tags: List[str]
    importance: float  # 0.0 to 1.0
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    embedding: Optional[List[float]] = None
    related_items: List[str] = None


@dataclass
class SearchResult:
    """Represents a search result with relevance scoring."""
    item: MemoryItem
    relevance_score: float
    match_type: str  # exact, semantic, fuzzy, etc.
    match_context: str
    highlights: List[str] = None


class MemoryEngine(BaseTool):
    """
    Enhanced Memory Engine with intelligent storage and retrieval.
    
    Provides persistent memory management, semantic search, and learning
    capabilities to enable intelligent AI assistance with long-term memory.
    """
    
    def __init__(self):
        self._name = "memory_engine"
        self._description = "Enhanced memory and search engine with AI capabilities"
        super().__init__()
        self.logger = get_logger("memory_engine")
        self.settings = get_settings()

    @property
    def name(self) -> str:
        """Get the tool name."""
        return "memory_engine"

    @property
    def description(self) -> str:
        """Get the tool description."""
        return "Enhanced memory management with persistent storage and AI-powered retrieval"

    @property
    def category(self) -> ToolCategory:
        """Get the tool category."""
        return ToolCategory.MEMORY

    @property
    def parameters(self) -> List[ToolParameter]:
        """Get the tool parameters schema."""
        return [
            ToolParameter(
                name="action",
                type="string",
                description="Action to perform",
                required=False,
                default="search",
                choices=["remember", "search", "stats"]
            ),
            ToolParameter(
                name="content",
                type="string",
                description="Content to remember (for remember action)",
                required=False
            ),
            ToolParameter(
                name="query",
                type="string",
                description="Search query (for search action)",
                required=False
            ),
            ToolParameter(
                name="memory_type",
                type="string",
                description="Type of memory",
                required=False,
                default="general"
            ),
            ToolParameter(
                name="importance",
                type="number",
                description="Importance score (0-1)",
                required=False,
                default=0.5
            ),
            ToolParameter(
                name="tags",
                type="array",
                description="List of tags",
                required=False,
                default=[]
            )
        ]

    def __init__(self):
        """Initialize the enhanced memory engine."""
        super().__init__()
        self.logger = get_logger("memory_engine")
        self.settings = get_settings()
        
        # Initialize persistent storage
        self.db_path = Path("data/memory.db")
        self.db_path.parent.mkdir(exist_ok=True)
        self._init_database()
        
        # Memory management settings
        self.max_memory_items = 100000
        self.cleanup_threshold = 0.8  # Cleanup when 80% full
        self.importance_decay_rate = 0.95  # Daily decay rate
        self.min_importance_threshold = 0.1
        
        # Search settings
        self.max_search_results = 50
        self.semantic_similarity_threshold = 0.7
        self.fuzzy_match_threshold = 0.6
        
        # Cache for frequently accessed items
        self.memory_cache: Dict[str, MemoryItem] = {}
        self.cache_size_limit = 1000
        
        # Search indices
        self.keyword_index: Dict[str, List[str]] = defaultdict(list)
        self.tag_index: Dict[str, List[str]] = defaultdict(list)
        self.type_index: Dict[str, List[str]] = defaultdict(list)
        
        # Load indices from database
        asyncio.create_task(self._load_indices())
        
        self.logger.info("Enhanced MemoryEngine initialized with persistent storage and search capabilities")
    
    def _init_database(self):
        """Initialize SQLite database for persistent memory storage."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Memory items table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS memory_items (
                        id TEXT PRIMARY KEY,
                        type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        metadata TEXT,
                        tags TEXT,
                        importance REAL,
                        created_at DATETIME,
                        last_accessed DATETIME,
                        access_count INTEGER DEFAULT 0,
                        embedding BLOB,
                        related_items TEXT
                    )
                """)
                
                # Search indices table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS search_indices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        index_type TEXT,
                        key TEXT,
                        item_ids TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # User interactions table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS user_interactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        interaction_type TEXT,
                        content TEXT,
                        context TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                """)
                
                # Learning patterns table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS learning_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pattern_type TEXT,
                        pattern_data TEXT,
                        confidence REAL,
                        usage_count INTEGER DEFAULT 0,
                        last_used DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indices for better performance
                conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_items(type)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_importance ON memory_items(importance)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_created ON memory_items(created_at)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_accessed ON memory_items(last_accessed)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_search_type ON search_indices(index_type)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_interactions_type ON user_interactions(interaction_type)")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize memory database: {e}")
    
    async def smart_remember(
        self,
        content: str,
        memory_type: str = "general",
        context: Optional[str] = None,
        importance: float = 0.5,
        tags: Optional[List[str]] = None
    ) -> ToolResult:
        """
        One-line smart memory storage with automatic indexing and relationship detection.
        
        Args:
            content: Content to remember
            memory_type: Type of memory (conversation, code, web, insight, etc.)
            context: Additional context for better understanding
            importance: Importance score (0.0 to 1.0)
            tags: Optional tags for categorization
        """
        try:
            self.logger.info(f"Smart remembering {memory_type} content")
            
            # Generate unique ID
            content_hash = hashlib.sha256(content.encode()).hexdigest()
            memory_id = f"{memory_type}_{content_hash[:16]}"
            
            # Extract metadata and enhance with AI analysis
            metadata = await self._extract_content_metadata(content, context)
            
            # Auto-generate tags if not provided
            if not tags:
                tags = await self._generate_smart_tags(content, memory_type, context)
            
            # Calculate enhanced importance score
            enhanced_importance = await self._calculate_enhanced_importance(
                content, memory_type, context, importance
            )
            
            # Create memory item
            memory_item = MemoryItem(
                id=memory_id,
                type=memory_type,
                content=content,
                metadata=metadata,
                tags=tags or [],
                importance=enhanced_importance,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                related_items=await self._find_related_items(content, memory_type)
            )
            
            # Generate embedding for semantic search
            memory_item.embedding = await self._generate_embedding(content)
            
            # Store in database and cache
            await self._store_memory_item(memory_item)
            
            # Update indices
            await self._update_search_indices(memory_item)
            
            # Learn from this interaction
            await self._learn_from_interaction("remember", content, context)
            
            return ToolResult(
                success=True,
                data={
                    "memory_id": memory_id,
                    "type": memory_type,
                    "importance": enhanced_importance,
                    "tags": tags,
                    "related_count": len(memory_item.related_items or [])
                },
                message=f"Smart memory stored with ID: {memory_id}"
            )
            
        except Exception as e:
            self.logger.error(f"Smart remember failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Smart remember failed"
            )
    
    async def intelligent_search(
        self,
        query: str,
        search_type: str = "smart",
        memory_types: Optional[List[str]] = None,
        max_results: int = 10,
        include_related: bool = True
    ) -> ToolResult:
        """
        One-line intelligent search across all memory with semantic understanding.
        
        Args:
            query: Search query
            search_type: Type of search ("smart", "semantic", "exact", "fuzzy")
            memory_types: Filter by memory types
            max_results: Maximum number of results
            include_related: Include related items in results
        """
        try:
            self.logger.info(f"Intelligent search for: {query}")
            
            # Enhance query with context and synonyms
            enhanced_query = await self._enhance_search_query(query)
            
            # Perform multi-modal search
            search_results = []
            
            if search_type in ["smart", "semantic"]:
                semantic_results = await self._semantic_search(enhanced_query, memory_types, max_results)
                search_results.extend(semantic_results)
            
            if search_type in ["smart", "exact"]:
                exact_results = await self._exact_search(enhanced_query, memory_types, max_results)
                search_results.extend(exact_results)
            
            if search_type in ["smart", "fuzzy"]:
                fuzzy_results = await self._fuzzy_search(enhanced_query, memory_types, max_results)
                search_results.extend(fuzzy_results)
            
            # Remove duplicates and sort by relevance
            unique_results = self._deduplicate_results(search_results)
            sorted_results = sorted(unique_results, key=lambda x: x.relevance_score, reverse=True)
            
            # Limit results
            final_results = sorted_results[:max_results]
            
            # Include related items if requested
            if include_related:
                final_results = await self._include_related_items(final_results)
            
            # Update access patterns for learning
            await self._update_access_patterns(query, final_results)
            
            return ToolResult(
                success=True,
                data={
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "results": [self._serialize_search_result(result) for result in final_results],
                    "total_found": len(search_results),
                    "search_type": search_type
                },
                message=f"Intelligent search completed: {len(final_results)} results found"
            )
            
        except Exception as e:
            self.logger.error(f"Intelligent search failed: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                message="Intelligent search failed"
            )

    async def _extract_content_metadata(self, content: str, context: Optional[str] = None) -> Dict[str, Any]:
        """Extract metadata from content using AI analysis."""
        metadata = {
            "word_count": len(content.split()),
            "char_count": len(content),
            "language": self._detect_content_language(content),
            "content_type": self._classify_content_type(content),
            "key_entities": self._extract_key_entities(content),
            "sentiment": self._analyze_sentiment(content),
            "complexity": self._calculate_content_complexity(content),
            "topics": self._extract_topics(content)
        }

        if context:
            metadata["context"] = context
            metadata["context_relevance"] = self._calculate_context_relevance(content, context)

        return metadata

    async def _generate_smart_tags(self, content: str, memory_type: str, context: Optional[str] = None) -> List[str]:
        """Generate smart tags using AI analysis."""
        tags = [memory_type]

        # Content-based tags
        if "def " in content or "function " in content:
            tags.append("code")
        if "http" in content.lower() or "www" in content.lower():
            tags.append("web")
        if "?" in content and len(content.split()) < 20:
            tags.append("question")
        if "error" in content.lower() or "exception" in content.lower():
            tags.append("error")
        if "todo" in content.lower() or "fixme" in content.lower():
            tags.append("todo")

        # Extract technical terms as tags
        tech_terms = re.findall(r'\b[A-Z][a-z]*[A-Z][a-z]*\b', content)  # CamelCase
        tags.extend(tech_terms[:5])  # Limit to 5 technical terms

        # Context-based tags
        if context:
            context_words = re.findall(r'\b\w{4,}\b', context.lower())
            relevant_words = [word for word in context_words if word not in {'this', 'that', 'with', 'from'}]
            tags.extend(relevant_words[:3])

        return list(set(tags))  # Remove duplicates

    async def _calculate_enhanced_importance(
        self,
        content: str,
        memory_type: str,
        context: Optional[str],
        base_importance: float
    ) -> float:
        """Calculate enhanced importance score using multiple factors."""
        importance = base_importance

        # Type-based importance
        type_weights = {
            "conversation": 0.7,
            "code": 0.9,
            "insight": 0.8,
            "error": 0.6,
            "web": 0.5,
            "pattern": 0.8
        }
        importance *= type_weights.get(memory_type, 0.5)

        # Content-based importance
        if len(content) > 1000:  # Long content
            importance += 0.1
        if "important" in content.lower() or "critical" in content.lower():
            importance += 0.2
        if "error" in content.lower() or "bug" in content.lower():
            importance += 0.15

        # Recency boost
        importance += 0.1  # New items get a boost

        return min(1.0, importance)

    async def _find_related_items(self, content: str, memory_type: str) -> List[str]:
        """Find related memory items using content similarity."""
        related_items = []

        try:
            # Simple keyword-based relation finding
            content_words = set(re.findall(r'\b\w{4,}\b', content.lower()))

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT id, content FROM memory_items WHERE type = ? ORDER BY importance DESC LIMIT 100",
                    (memory_type,)
                )

                for item_id, item_content in cursor.fetchall():
                    item_words = set(re.findall(r'\b\w{4,}\b', item_content.lower()))

                    # Calculate word overlap
                    overlap = len(content_words.intersection(item_words))
                    if overlap >= 3:  # Minimum 3 common words
                        related_items.append(item_id)

                    if len(related_items) >= 10:  # Limit related items
                        break

        except Exception as e:
            self.logger.warning(f"Failed to find related items: {e}")

        return related_items

    async def _generate_embedding(self, content: str) -> List[float]:
        """Generate embedding for semantic search (placeholder implementation)."""
        # Simple hash-based embedding (can be replaced with actual embedding models)
        words = re.findall(r'\b\w+\b', content.lower())
        embedding = [0.0] * 128  # 128-dimensional embedding

        for i, word in enumerate(words[:128]):
            hash_val = hash(word) % 1000000
            embedding[i % 128] += hash_val / 1000000.0

        # Normalize
        magnitude = sum(x * x for x in embedding) ** 0.5
        if magnitude > 0:
            embedding = [x / magnitude for x in embedding]

        return embedding

    async def _store_memory_item(self, item: MemoryItem):
        """Store memory item in database and cache."""
        try:
            # Serialize complex fields
            metadata_json = json.dumps(item.metadata)
            tags_json = json.dumps(item.tags)
            related_items_json = json.dumps(item.related_items or [])
            embedding_blob = pickle.dumps(item.embedding) if item.embedding else None

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO memory_items
                    (id, type, content, metadata, tags, importance, created_at, last_accessed, access_count, embedding, related_items)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    item.id, item.type, item.content, metadata_json, tags_json,
                    item.importance, item.created_at, item.last_accessed,
                    item.access_count, embedding_blob, related_items_json
                ))

            # Add to cache
            self.memory_cache[item.id] = item

            # Manage cache size
            if len(self.memory_cache) > self.cache_size_limit:
                # Remove least recently accessed items
                sorted_items = sorted(
                    self.memory_cache.items(),
                    key=lambda x: x[1].last_accessed
                )
                for item_id, _ in sorted_items[:100]:  # Remove 100 oldest
                    del self.memory_cache[item_id]

        except Exception as e:
            self.logger.error(f"Failed to store memory item: {e}")

    async def _update_search_indices(self, item: MemoryItem):
        """Update search indices for the memory item."""
        try:
            # Extract keywords for indexing
            keywords = self._extract_keywords(item.content)

            # Update keyword index
            for keyword in keywords:
                if item.id not in self.keyword_index[keyword]:
                    self.keyword_index[keyword].append(item.id)

            # Update tag index
            for tag in item.tags:
                if item.id not in self.tag_index[tag]:
                    self.tag_index[tag].append(item.id)

            # Update type index
            if item.id not in self.type_index[item.type]:
                self.type_index[item.type].append(item.id)

            # Persist indices to database
            await self._persist_indices()

        except Exception as e:
            self.logger.warning(f"Failed to update search indices: {e}")

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from content for indexing."""
        # Simple keyword extraction
        words = re.findall(r'\b\w{3,}\b', content.lower())

        # Filter out common words
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        }

        keywords = [word for word in words if word not in stop_words and len(word) > 3]

        # Count frequency and return most common
        word_freq = Counter(keywords)
        return [word for word, freq in word_freq.most_common(20)]

    async def _semantic_search(self, query: str, memory_types: Optional[List[str]], max_results: int) -> List[SearchResult]:
        """Perform semantic search using embeddings."""
        results = []

        try:
            # Generate query embedding
            query_embedding = await self._generate_embedding(query)

            # Search through stored items
            with sqlite3.connect(self.db_path) as conn:
                type_filter = ""
                params = []

                if memory_types:
                    type_filter = f"WHERE type IN ({','.join(['?' for _ in memory_types])})"
                    params = memory_types

                cursor = conn.execute(f"""
                    SELECT id, type, content, metadata, tags, importance, embedding
                    FROM memory_items {type_filter}
                    ORDER BY importance DESC
                    LIMIT 1000
                """, params)

                for row in cursor.fetchall():
                    item_id, item_type, content, metadata_json, tags_json, importance, embedding_blob = row

                    if embedding_blob:
                        item_embedding = pickle.loads(embedding_blob)
                        similarity = self._calculate_cosine_similarity(query_embedding, item_embedding)

                        if similarity >= self.semantic_similarity_threshold:
                            # Create memory item
                            memory_item = MemoryItem(
                                id=item_id,
                                type=item_type,
                                content=content,
                                metadata=json.loads(metadata_json),
                                tags=json.loads(tags_json),
                                importance=importance,
                                created_at=datetime.now(),  # Placeholder
                                last_accessed=datetime.now()
                            )

                            results.append(SearchResult(
                                item=memory_item,
                                relevance_score=similarity,
                                match_type="semantic",
                                match_context=f"Semantic similarity: {similarity:.2f}"
                            ))

        except Exception as e:
            self.logger.warning(f"Semantic search failed: {e}")

        return sorted(results, key=lambda x: x.relevance_score, reverse=True)[:max_results]

    async def _exact_search(self, query: str, memory_types: Optional[List[str]], max_results: int) -> List[SearchResult]:
        """Perform exact text search."""
        results = []

        try:
            with sqlite3.connect(self.db_path) as conn:
                type_filter = ""
                params = [f"%{query}%"]

                if memory_types:
                    type_filter = f"AND type IN ({','.join(['?' for _ in memory_types])})"
                    params.extend(memory_types)

                cursor = conn.execute(f"""
                    SELECT id, type, content, metadata, tags, importance
                    FROM memory_items
                    WHERE content LIKE ? {type_filter}
                    ORDER BY importance DESC
                    LIMIT ?
                """, params + [max_results])

                for row in cursor.fetchall():
                    item_id, item_type, content, metadata_json, tags_json, importance = row

                    # Calculate relevance based on match position and frequency
                    query_lower = query.lower()
                    content_lower = content.lower()

                    match_count = content_lower.count(query_lower)
                    first_match_pos = content_lower.find(query_lower)

                    # Higher score for matches at the beginning and multiple matches
                    relevance = 0.8 + (0.2 * min(match_count, 5) / 5)
                    if first_match_pos < 100:  # Early match bonus
                        relevance += 0.1

                    memory_item = MemoryItem(
                        id=item_id,
                        type=item_type,
                        content=content,
                        metadata=json.loads(metadata_json),
                        tags=json.loads(tags_json),
                        importance=importance,
                        created_at=datetime.now(),
                        last_accessed=datetime.now()
                    )

                    results.append(SearchResult(
                        item=memory_item,
                        relevance_score=relevance,
                        match_type="exact",
                        match_context=f"Exact match found {match_count} times",
                        highlights=[query]
                    ))

        except Exception as e:
            self.logger.warning(f"Exact search failed: {e}")

        return results

    async def _fuzzy_search(self, query: str, memory_types: Optional[List[str]], max_results: int) -> List[SearchResult]:
        """Perform fuzzy search using keyword matching."""
        results = []

        try:
            query_keywords = self._extract_keywords(query)

            if not query_keywords:
                return results

            # Search using keyword index
            candidate_items = set()
            for keyword in query_keywords:
                if keyword in self.keyword_index:
                    candidate_items.update(self.keyword_index[keyword])

            # Score candidates
            for item_id in candidate_items:
                try:
                    memory_item = await self._get_memory_item(item_id)
                    if memory_item and (not memory_types or memory_item.type in memory_types):

                        # Calculate fuzzy relevance
                        item_keywords = self._extract_keywords(memory_item.content)
                        common_keywords = set(query_keywords).intersection(set(item_keywords))

                        if common_keywords:
                            relevance = len(common_keywords) / len(query_keywords)

                            if relevance >= self.fuzzy_match_threshold:
                                results.append(SearchResult(
                                    item=memory_item,
                                    relevance_score=relevance,
                                    match_type="fuzzy",
                                    match_context=f"Keyword match: {', '.join(common_keywords)}",
                                    highlights=list(common_keywords)
                                ))

                except Exception as e:
                    self.logger.warning(f"Error processing candidate {item_id}: {e}")

        except Exception as e:
            self.logger.warning(f"Fuzzy search failed: {e}")

        return sorted(results, key=lambda x: x.relevance_score, reverse=True)[:max_results]

    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        if len(vec1) != len(vec2):
            return 0.0

        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5

        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        return dot_product / (magnitude1 * magnitude2)

    async def _get_memory_item(self, item_id: str) -> Optional[MemoryItem]:
        """Get memory item from cache or database."""
        # Check cache first
        if item_id in self.memory_cache:
            return self.memory_cache[item_id]

        # Load from database
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT id, type, content, metadata, tags, importance,
                           created_at, last_accessed, access_count, embedding, related_items
                    FROM memory_items WHERE id = ?
                """, (item_id,))

                row = cursor.fetchone()
                if row:
                    (id, type, content, metadata_json, tags_json, importance,
                     created_at, last_accessed, access_count, embedding_blob, related_items_json) = row

                    memory_item = MemoryItem(
                        id=id,
                        type=type,
                        content=content,
                        metadata=json.loads(metadata_json),
                        tags=json.loads(tags_json),
                        importance=importance,
                        created_at=datetime.fromisoformat(created_at) if created_at else datetime.now(),
                        last_accessed=datetime.fromisoformat(last_accessed) if last_accessed else datetime.now(),
                        access_count=access_count,
                        embedding=pickle.loads(embedding_blob) if embedding_blob else None,
                        related_items=json.loads(related_items_json) if related_items_json else []
                    )

                    # Add to cache
                    self.memory_cache[item_id] = memory_item
                    return memory_item

        except Exception as e:
            self.logger.warning(f"Failed to get memory item {item_id}: {e}")

        return None

    def _serialize_search_result(self, result: SearchResult) -> Dict[str, Any]:
        """Serialize search result for API response."""
        return {
            "item": {
                "id": result.item.id,
                "type": result.item.type,
                "content": result.item.content[:500],  # Truncate for response
                "metadata": result.item.metadata,
                "tags": result.item.tags,
                "importance": result.item.importance,
                "created_at": result.item.created_at.isoformat(),
                "last_accessed": result.item.last_accessed.isoformat()
            },
            "relevance_score": result.relevance_score,
            "match_type": result.match_type,
            "match_context": result.match_context,
            "highlights": result.highlights or []
        }

    # Placeholder methods for content analysis
    def _detect_content_language(self, content: str) -> str:
        """Detect content language (placeholder)."""
        return "en"  # Default to English

    def _classify_content_type(self, content: str) -> str:
        """Classify content type (placeholder)."""
        if "def " in content or "function " in content:
            return "code"
        elif "http" in content.lower():
            return "web"
        elif "?" in content and len(content.split()) < 20:
            return "question"
        else:
            return "text"

    def _extract_key_entities(self, content: str) -> List[str]:
        """Extract key entities (placeholder)."""
        # Simple entity extraction
        entities = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', content)
        return entities[:10]

    def _analyze_sentiment(self, content: str) -> str:
        """Analyze sentiment (placeholder)."""
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic']
        negative_words = ['bad', 'terrible', 'awful', 'horrible', 'disappointing', 'frustrating']

        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)

        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"

    def _calculate_content_complexity(self, content: str) -> float:
        """Calculate content complexity (placeholder)."""
        # Simple complexity based on length and structure
        words = content.split()
        sentences = content.count('.') + content.count('!') + content.count('?')

        if sentences == 0:
            return 0.5

        avg_sentence_length = len(words) / sentences
        complexity = min(1.0, avg_sentence_length / 20)  # Normalize to 0-1

        return complexity

    def _extract_topics(self, content: str) -> List[str]:
        """Extract topics (placeholder)."""
        # Simple topic extraction based on frequent words
        keywords = self._extract_keywords(content)
        return keywords[:5]  # Return top 5 as topics

    async def execute(self, **kwargs) -> ToolResult:
        """Execute memory engine operations based on the action parameter.

        Args:
            **kwargs: Operation parameters including:
                - action: The operation to perform ('remember', 'search', 'stats')
                - content: Content to remember (for remember action)
                - query: Search query (for search action)
                - memory_type: Type of memory
                - importance: Importance score
                - tags: List of tags
                - Other action-specific parameters

        Returns:
            ToolResult with operation results
        """
        try:
            action = kwargs.get('action', 'search')

            if action == 'remember':
                content = kwargs.get('content', '')
                memory_type = kwargs.get('memory_type', 'general')
                importance = kwargs.get('importance', 0.5)
                tags = kwargs.get('tags', [])

                return await self.smart_remember(
                    content=content,
                    memory_type=memory_type,
                    importance=importance,
                    tags=tags
                )

            elif action == 'search':
                query = kwargs.get('query', '')
                search_type = kwargs.get('search_type', 'smart')
                max_results = kwargs.get('max_results', 10)

                return await self.intelligent_search(
                    query=query,
                    search_type=search_type,
                    max_results=max_results
                )

            elif action == 'stats':
                # Return memory statistics
                return ToolResult(
                    success=True,
                    data={
                        "message": "Memory statistics",
                        "total_items": self._get_memory_count(),
                        "database_path": str(self.db_path)
                    }
                )

            else:
                return ToolResult(
                    success=False,
                    error=f"Unknown action: {action}"
                )

        except Exception as e:
            self.logger.error(f"Memory engine execution failed: {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )

    def _get_memory_count(self) -> int:
        """Get the total number of memory items."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM memories")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception:
            return 0
