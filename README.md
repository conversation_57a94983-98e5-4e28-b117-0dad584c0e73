# 🌟 Reverie CLI

**AI-native code development server with Augment-like capabilities**

Reverie CLI is a comprehensive AI-powered development environment that combines the power of modern AI models with an intuitive development experience. It provides both a FastAPI web server and an interactive CLI with AI coding assistant capabilities.

## ✨ Features

### 🤖 Dual-Mode Operation
- **Server Mode**: FastAPI web server with REST API endpoints
- **AI Coder Mode**: Interactive CLI with direct AI assistance
- **Unified Console**: Real-time command interface supporting both modes

### 🧠 AI Model Management
- **Multi-Backend Support**: Transformers, vLLM, GGUF, TensorFlow
- **Default Model**: Lucy-128k with 128k context length
- **Popular Models**: Llama 2, Mistral, Code Llama, Phi-3, Gemma
- **Smart Loading**: Automatic backend detection and optimization

### 🎯 Intelligent Agent System
- **Task Decomposition**: Automatic breakdown of complex tasks
- **Tool Integration**: File operations, web search, code execution
- **Context Understanding**: Advanced code analysis and comprehension
- **Memory Management**: Long-term learning and pattern recognition

### 🛠️ Enhanced AI Engines

#### 🌐 Web Engine
- **Smart Search**: Multi-engine web search with AI analysis (Google, Bing, DuckDuckGo, Searx)
- **Intelligent Extraction**: AI-powered content extraction and summarization
- **Real-time Monitoring**: Website change detection and monitoring
- **Persistent Caching**: SQLite-based caching for optimal performance

#### 🔍 Context Engine
- **Deep Code Analysis**: Comprehensive codebase understanding with AI insights
- **Multi-Language Support**: Python, JavaScript, TypeScript, Java, C++, Go, Rust, Ruby, PHP, C#
- **Framework Detection**: Automatic detection of frameworks and technologies
- **Quality Assessment**: Code quality, complexity, and security analysis

#### 🧠 Memory Engine
- **Persistent Learning**: SQLite-based memory with intelligent indexing
- **Semantic Search**: Vector-based similarity search for content
- **Pattern Recognition**: Learning from user interactions and preferences
- **Context Awareness**: Project and conversation context understanding

### 🎪 One-Line Complex Operations
```python
# Smart composite functions for maximum efficiency
smart_solution(problem) = web_search(problem) + recall(similar_issues) + code_analyze(context) + generate_solution()
project_health(path) = project_overview(path) + dependency_audit(path) + performance_analyze(path) + remember(results)
full_analysis(path) = code_analyze(path) + code_quality(path) + security_audit(path) + remember(insights)
```

### 🛠️ Comprehensive Tool Suite
- **File Operations**: Create, read, write, delete files and directories
- **Web Search**: Real-time information retrieval with AI enhancement
- **Code Execution**: Multi-language code execution environment
- **Git Integration**: Repository management and version control

### 🌐 Modern Web Interface
- **Real-time Chat**: Interactive AI conversation interface
- **System Monitoring**: Live status and performance metrics
- **API Documentation**: Integrated Swagger/OpenAPI docs
- **Responsive Design**: Works on desktop and mobile

## 🚀 Quick Start

### Windows 快速部署 (推荐)

```bash
# 1. 设置环境
setup.bat

# 2. 启动应用
start.bat

# 或者使用命令行参数直接启动
start.bat --server          # 仅启动服务器
start.bat --console         # 仅启动控制台
start.bat --dual            # 同时启动服务器和控制台
start.bat --help            # 查看所有命令和帮助
```

### 手动安装

```bash
# Clone the repository
git clone https://github.com/rilance/reverie-cli.git
cd reverie-cli

# Create virtual environment
python -m venv reverie_env
reverie_env\Scripts\activate  # Windows
# source reverie_env/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt
pip install -e .

# Or install with optional dependencies
pip install -e ".[dev,vllm,gguf]"
```

#### 🔄 Flexible Dependencies
All Python library dependencies are configured **without version constraints** for maximum compatibility:
- ✅ Uses latest available versions of all libraries
- ✅ Avoids version conflicts with existing installations
- ✅ Easy integration with different Python environments
- ✅ Compatible across different operating systems

### Basic Usage

```bash
# Start the server with interactive console
reverie start

# Start server only (no interactive console)
reverie start --no-interactive

# Custom host and port
reverie start --host 0.0.0.0 --port 8080

# Show configuration
reverie config

# Show version
reverie version
```

### Interactive Console Commands

Once the server is running, you can use these commands in the interactive console:

```bash
# Model management
reverie> models              # List available models
reverie> load Lucy-128k      # Load a specific model
reverie> unload             # Unload current model
reverie> download mistral   # Download a model

# Server management
reverie> status             # Show system status
reverie> memory             # Show memory usage
reverie> stop               # Stop the server

# AI assistance
reverie> chat               # Start chat mode
reverie> code "create a web scraper"  # Generate code
reverie> help               # Show all commands
```

## 📡 API Endpoints

### Core Endpoints
- `GET /` - Web interface
- `GET /api/info` - API information
- `GET /docs` - Interactive API documentation

### Health & Status
- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed system status

### Model Management
- `GET /api/v1/models` - List available models
- `POST /api/v1/models/load` - Load a model
- `POST /api/v1/models/unload` - Unload current model
- `GET /api/v1/models/current` - Get current model info

### Chat & Completion
- `POST /api/v1/chat/completions` - Chat completions (OpenAI compatible)
- `POST /api/v1/chat/enhanced` - Enhanced chat with AI engine integration
- `POST /api/v1/chat/smart-code` - Intelligent code assistance
- `POST /api/v1/completions` - Text completions

### Enhanced Agent System
- `POST /api/v1/agent/execute` - Execute complex tasks with AI engines
- `POST /api/v1/agent/smart-assist` - Intelligent assistance with auto-engine selection
- `GET /api/v1/agent/capabilities` - Get current capabilities and engines
- `GET /api/v1/agent/status/{task_id}` - Get task status

### Tools & Files
- `GET /api/v1/tools` - List available tools
- `POST /api/v1/tools/execute` - Execute a tool
- `GET /api/v1/files` - List files
- `POST /api/v1/files/create` - Create file

## ⚙️ Configuration

### Environment Variables

```bash
# API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
HUGGINGFACE_TOKEN=your_hf_token

# Server Configuration
SERVER__HOST=127.0.0.1
SERVER__PORT=8000
SERVER__WORKERS=1

# Model Configuration
MODEL__DEFAULT_MODEL=Menlo/Lucy-128k-gguf
MODEL__MAX_CONTEXT_LENGTH=131072
MODEL__TEMPERATURE=0.7

# Agent Configuration
AGENT__MAX_ITERATIONS=50
AGENT__MEMORY_ENABLED=true
AGENT__ENABLE_CODE_ANALYSIS=true
```

### Configuration File

Create a `.env` file in the project root:

```env
# Server settings
SERVER__HOST=0.0.0.0
SERVER__PORT=8000
SERVER__RELOAD=false

# Model settings
MODEL__DEFAULT_MODEL=Menlo/Lucy-128k-gguf
MODEL__CACHE_DIR=~/.cache/reverie_cli/models
MODEL__TRANSFORMERS_LOAD_IN_4BIT=true

# Logging
LOGGING__LEVEL=INFO
LOGGING__FILE_ENABLED=true
LOGGING__FILE_PATH=logs/reverie_cli.log
```

## 🏗️ Architecture

```
reverie_cli/
├── core/                   # Core functionality
│   ├── config.py          # Configuration management
│   ├── logging.py         # Enhanced logging
│   ├── console.py         # Interactive console
│   └── exceptions.py      # Error handling
├── api/                   # FastAPI server
│   ├── server.py          # Main server
│   └── routes/            # API endpoints
├── models/                # AI model management
├── agent/                 # AI agent system
├── tools/                 # Tool implementations
├── plugins/               # Plugin system
├── static/                # Web assets
└── templates/             # HTML templates
```

## 🔧 Development

### Setup Development Environment

```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run with auto-reload
reverie start --reload
```

### Code Quality

```bash
# Format code
black reverie_cli/
isort reverie_cli/

# Lint code
flake8 reverie_cli/
mypy reverie_cli/

# Run all checks
pre-commit run --all-files
```

## 📚 Examples

### Using the Chat API

```python
import requests

response = requests.post("http://localhost:8000/api/v1/chat/completions", json={
    "messages": [
        {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "temperature": 0.7,
    "max_tokens": 1000
})

print(response.json()["choices"][0]["message"]["content"])
```

### Using the Agent API

```python
import requests

response = requests.post("http://localhost:8000/api/v1/agent/execute", json={
    "task": "Create a simple web scraper for news headlines",
    "context": {"language": "python", "framework": "requests+beautifulsoup"}
})

print(response.json())
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [Gemini CLI](https://github.com/google-gemini/gemini-cli)
- Built with [FastAPI](https://fastapi.tiangolo.com/)
- UI powered by [Tailwind CSS](https://tailwindcss.com/)
- Logging with [Loguru](https://github.com/Delgan/loguru)

---

**Built with ❤️ by Rilance Code Studio**
