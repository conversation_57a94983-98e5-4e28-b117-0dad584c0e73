# 👨‍💻 Reverie CLI - 开发者指南

## 🎯 概述

本指南面向希望扩展、自定义或贡献Reverie CLI的开发者。涵盖架构设计、代码结构、扩展开发和贡献流程。

## 🏗️ 架构概览

### 核心架构
```
Reverie CLI
├── 🎪 Dual-Mode Interface
│   ├── AI Coder Mode (自然语言交互)
│   └── API Service Mode (REST API服务)
├── 🧠 Enhanced Engines
│   ├── Web Engine (网络搜索和内容分析)
│   ├── Context Engine (代码分析和理解)
│   └── Memory Engine (持久化学习和记忆)
├── 🤖 Model Management
│   ├── Multi-Backend Support
│   └── Dynamic Model Loading
└── 🔧 Tool System
    ├── Built-in Tools
    └── Plugin Architecture
```

### 技术栈
- **后端**: Python 3.8+, FastAPI, SQLAlchemy
- **AI/ML**: PyTorch, Transformers, GGUF
- **数据库**: SQLite (默认), PostgreSQL (可选)
- **缓存**: 内存缓存 + 文件缓存
- **前端**: HTML5, JavaScript, CSS3
- **部署**: Docker, 虚拟环境

## 📁 项目结构

```
reverie_cli/
├── __init__.py                 # 包初始化
├── main.py                     # 主入口点
├── agent/                      # AI代理系统
│   ├── __init__.py
│   ├── engine.py              # 代理引擎
│   ├── prompts.py             # 提示管理
│   └── tasks.py               # 任务处理
├── api/                        # REST API
│   ├── __init__.py
│   ├── server.py              # FastAPI服务器
│   ├── dependencies.py        # 依赖注入
│   ├── models.py              # API数据模型
│   └── routes/                # API路由
│       ├── __init__.py
│       ├── chat.py            # 聊天API
│       ├── enhanced_agent.py  # 增强代理API
│       ├── health.py          # 健康检查
│       ├── models.py          # 模型管理
│       ├── tools.py           # 工具API
│       ├── files.py           # 文件操作
│       └── config.py          # 配置管理
├── config/                     # 配置管理
│   ├── __init__.py
│   ├── settings.py            # 设置管理
│   └── defaults.py            # 默认配置
├── core/                       # 核心功能
│   ├── __init__.py
│   ├── console.py             # 双模式控制台
│   ├── logging.py             # 日志系统
│   └── exceptions.py          # 异常处理
├── models/                     # 模型管理
│   ├── __init__.py
│   ├── manager.py             # 模型管理器
│   ├── backends/              # 模型后端
│   │   ├── __init__.py
│   │   ├── transformers.py    # Transformers后端
│   │   ├── vllm.py           # vLLM后端
│   │   └── gguf.py           # GGUF后端
│   └── registry.py            # 模型注册表
├── plugins/                    # 插件系统
│   ├── __init__.py
│   ├── base.py               # 插件基类
│   └── manager.py            # 插件管理器
├── static/                     # 静态文件
│   ├── css/
│   ├── js/
│   └── images/
├── templates/                  # HTML模板
│   ├── base.html
│   ├── chat.html
│   └── index.html
└── tools/                      # 增强工具引擎
    ├── __init__.py
    ├── base.py               # 工具基类
    ├── manager.py            # 工具管理器
    ├── registry.py           # 工具注册表
    ├── web_engine.py         # Web引擎
    ├── context_engine.py     # Context引擎
    ├── memory_engine.py      # Memory引擎
    ├── file_operations.py    # 文件操作
    └── code_execution.py     # 代码执行
```

## 🔧 开发环境设置

### 1. 克隆和设置
```bash
# 克隆仓库
git clone https://github.com/your-org/reverie-cli.git
cd reverie-cli

# 设置开发环境
setup.bat  # Windows
# 或
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements-dev.txt
pip install -e .
```

### 2. 开发依赖
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 包含:
# - pytest (测试框架)
# - black (代码格式化)
# - flake8 (代码检查)
# - mypy (类型检查)
# - pre-commit (Git钩子)
```

### 3. 预提交钩子
```bash
# 安装预提交钩子
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

## 🧠 核心组件开发

### Enhanced Engines 开发

#### 创建新引擎
```python
# tools/my_engine.py
from typing import Dict, Any, Optional
from .base import BaseTool, ToolResult

class MyEngine(BaseTool):
    """自定义引擎示例"""
    
    def __init__(self):
        super().__init__()
        self.name = "my_engine"
        self.description = "My custom engine"
        self.version = "1.0.0"
    
    async def initialize(self) -> bool:
        """初始化引擎"""
        try:
            # 初始化逻辑
            self.logger.info("MyEngine initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize MyEngine: {e}")
            return False
    
    async def execute(self, **kwargs) -> ToolResult:
        """执行引擎功能"""
        try:
            # 实现核心功能
            result_data = await self._process_request(**kwargs)
            
            return ToolResult(
                success=True,
                data=result_data,
                metadata={
                    "engine": self.name,
                    "version": self.version
                }
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def _process_request(self, **kwargs) -> Dict[str, Any]:
        """处理请求的核心逻辑"""
        # 实现具体功能
        return {"result": "success"}

# 注册引擎
from .registry import tool_registry
tool_registry.register("my_engine", MyEngine)
```

#### 扩展现有引擎
```python
# tools/enhanced_web_engine.py
from .web_engine import WebEngine

class EnhancedWebEngine(WebEngine):
    """扩展的Web引擎"""
    
    async def advanced_search(self, query: str, **kwargs) -> ToolResult:
        """高级搜索功能"""
        # 调用基础搜索
        basic_result = await self.smart_search(query, **kwargs)
        
        if basic_result.success:
            # 添加额外处理
            enhanced_data = await self._enhance_results(basic_result.data)
            
            return ToolResult(
                success=True,
                data=enhanced_data,
                metadata={
                    **basic_result.metadata,
                    "enhanced": True
                }
            )
        
        return basic_result
    
    async def _enhance_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """增强搜索结果"""
        # 实现结果增强逻辑
        return data
```

### API路由开发

#### 创建新API路由
```python
# api/routes/my_route.py
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.tools.manager import get_tool_manager

router = APIRouter()
logger = get_logger("my_route")

class MyRequest(BaseModel):
    """请求模型"""
    query: str = Field(..., description="查询内容")
    options: Optional[Dict[str, Any]] = Field(None, description="可选参数")

class MyResponse(BaseModel):
    """响应模型"""
    result: str = Field(..., description="处理结果")
    metadata: Dict[str, Any] = Field(..., description="元数据")

@router.post("/my-endpoint", response_model=MyResponse)
async def my_endpoint(
    request: MyRequest,
    tool_manager=Depends(get_tool_manager)
):
    """自定义API端点"""
    try:
        # 获取工具
        my_tool = tool_manager.get_tool("my_engine")
        if not my_tool:
            raise HTTPException(status_code=503, detail="My engine not available")
        
        # 执行处理
        result = await my_tool.execute(
            query=request.query,
            **(request.options or {})
        )
        
        if result.success:
            return MyResponse(
                result=result.data.get("result", ""),
                metadata=result.metadata or {}
            )
        else:
            raise HTTPException(status_code=500, detail=result.error)
    
    except Exception as e:
        logger.error(f"My endpoint error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# 在 api/routes/__init__.py 中注册
from . import my_route
__all__.append("my_route")
```

### 插件开发

#### 创建插件
```python
# plugins/my_plugin.py
from typing import Dict, Any
from .base import BasePlugin

class MyPlugin(BasePlugin):
    """自定义插件示例"""
    
    def __init__(self):
        super().__init__()
        self.name = "my_plugin"
        self.version = "1.0.0"
        self.description = "My custom plugin"
    
    async def initialize(self) -> bool:
        """初始化插件"""
        try:
            # 插件初始化逻辑
            self.logger.info(f"Plugin {self.name} initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize plugin {self.name}: {e}")
            return False
    
    async def execute(self, command: str, **kwargs) -> Dict[str, Any]:
        """执行插件命令"""
        if command == "hello":
            return await self._hello(**kwargs)
        elif command == "process":
            return await self._process(**kwargs)
        else:
            raise ValueError(f"Unknown command: {command}")
    
    async def _hello(self, name: str = "World") -> Dict[str, Any]:
        """Hello命令"""
        return {
            "message": f"Hello, {name}!",
            "plugin": self.name
        }
    
    async def _process(self, data: Any) -> Dict[str, Any]:
        """处理命令"""
        # 实现数据处理逻辑
        return {
            "processed": True,
            "data": data
        }
    
    def get_commands(self) -> List[str]:
        """获取支持的命令"""
        return ["hello", "process"]

# 注册插件
from .manager import plugin_manager
plugin_manager.register("my_plugin", MyPlugin)
```

## 🧪 测试开发

### 单元测试
```python
# tests/test_my_engine.py
import pytest
import asyncio
from reverie_cli.tools.my_engine import MyEngine

class TestMyEngine:
    """MyEngine测试类"""
    
    @pytest.fixture
    async def engine(self):
        """创建引擎实例"""
        engine = MyEngine()
        await engine.initialize()
        return engine
    
    @pytest.mark.asyncio
    async def test_basic_functionality(self, engine):
        """测试基本功能"""
        result = await engine.execute(query="test")
        
        assert result.success
        assert "result" in result.data
        assert result.data["result"] == "success"
    
    @pytest.mark.asyncio
    async def test_error_handling(self, engine):
        """测试错误处理"""
        result = await engine.execute(invalid_param="test")
        
        assert not result.success
        assert result.error is not None
    
    def test_initialization(self):
        """测试初始化"""
        engine = MyEngine()
        assert engine.name == "my_engine"
        assert engine.version == "1.0.0"
```

### 集成测试
```python
# tests/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from reverie_cli.api.server import create_app

class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        app = create_app()
        return TestClient(app)
    
    def test_health_check(self, client):
        """测试健康检查"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_my_endpoint(self, client):
        """测试自定义端点"""
        request_data = {
            "query": "test query",
            "options": {"param": "value"}
        }
        
        response = client.post("/api/v1/my-endpoint", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "result" in data
        assert "metadata" in data
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_my_engine.py

# 运行带覆盖率的测试
pytest --cov=reverie_cli

# 运行性能测试
pytest tests/test_performance.py -v
```

## 📦 构建和部署

### 本地构建
```bash
# 构建包
python setup.py sdist bdist_wheel

# 安装本地包
pip install dist/reverie_cli-*.whl
```

### Docker部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .
RUN pip install -e .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "reverie_cli.main", "--host", "0.0.0.0", "--port", "8000"]
```

```bash
# 构建Docker镜像
docker build -t reverie-cli:latest .

# 运行容器
docker run -p 8000:8000 reverie-cli:latest
```

## 🔍 调试和性能优化

### 调试技巧
```python
# 启用调试日志
import logging
logging.getLogger("reverie_cli").setLevel(logging.DEBUG)

# 使用调试器
import pdb; pdb.set_trace()

# 异步调试
import asyncio
import aiotools
```

### 性能监控
```python
# tools/performance.py
import time
import psutil
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.info(f"{func.__name__} - Time: {execution_time:.2f}s, Memory: {memory_delta/1024/1024:.2f}MB")
    
    return wrapper

# 使用示例
@monitor_performance
async def my_function():
    # 函数实现
    pass
```

## 🤝 贡献指南

### 代码规范
1. **PEP 8**: 遵循Python代码规范
2. **类型注解**: 使用类型注解提高代码可读性
3. **文档字符串**: 为所有公共函数和类添加文档
4. **测试覆盖**: 新功能必须包含测试

### 提交流程
1. **Fork仓库**: 创建个人分支
2. **创建分支**: `git checkout -b feature/my-feature`
3. **编写代码**: 实现功能并添加测试
4. **运行测试**: 确保所有测试通过
5. **提交代码**: 使用清晰的提交信息
6. **创建PR**: 提交Pull Request

### 代码审查
- 所有PR需要至少一个审查者批准
- 自动化测试必须通过
- 代码覆盖率不能降低
- 文档必须更新

## 📚 扩展资源

### 相关文档
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [PyTorch文档](https://pytorch.org/docs/)
- [SQLAlchemy文档](https://docs.sqlalchemy.org/)

### 社区资源
- [GitHub Issues](https://github.com/your-org/reverie-cli/issues)
- [讨论区](https://github.com/your-org/reverie-cli/discussions)
- [Wiki](https://github.com/your-org/reverie-cli/wiki)

这个开发者指南为希望扩展或贡献Reverie CLI的开发者提供了全面的技术指导。
