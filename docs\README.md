# 📚 Reverie CLI - 完整文档索引

## 🎯 文档概览

欢迎来到Reverie CLI的完整文档中心！这里包含了使用、开发和扩展Reverie CLI所需的所有信息。

## 📖 文档结构

### 🚀 用户文档

#### [📖 用户指南 (USER_GUIDE.md)](./USER_GUIDE.md)
**适用对象**: 所有用户  
**内容概要**:
- 🎯 快速开始和环境要求
- 🎪 双模式操作详解 (AI Coder + API Service)
- 🌐 Enhanced Engines 完整使用指南
  - Web Engine: 智能网络搜索和内容分析
  - Context Engine: 深度代码分析和项目理解
  - Memory Engine: 持久化学习和智能记忆
- 🎪 一行复杂操作示例
- 🌐 Web界面使用说明
- 🔧 高级配置和自定义
- 📊 性能优化技巧

#### [💻 CMD/PowerShell 调用指南 (CMD_POWERSHELL_GUIDE.md)](./CMD_POWERSHELL_GUIDE.md)
**适用对象**: 需要命令行集成的用户  
**内容概要**:
- 🌐 Web Engine 命令行调用
  - 基本搜索、多引擎搜索、内容提取
  - 批量URL处理、搜索结果分析
- 🧠 Context Engine 命令行调用
  - 项目分析、代码质量检查、依赖分析
- 💾 Memory Engine 命令行调用
  - 记忆存储检索、智能搜索、记忆管理
- 🎪 组合操作示例
- 🔧 实用脚本模板
- 📊 性能监控脚本

#### [🔌 API 参考文档 (API_REFERENCE.md)](./API_REFERENCE.md)
**适用对象**: API集成开发者  
**内容概要**:
- 🌐 基础API信息和认证
- 🏥 健康检查API
- 🤖 模型管理API
- 💬 聊天和对话API (标准 + 增强)
- 🤖 增强代理API
- 🛠️ 工具管理API
- 📁 文件操作API
- ⚙️ 配置管理API
- 🔧 高级功能 (流式响应、批量操作、异步任务)
- 🚨 错误处理和安全最佳实践

### 🛠️ 开发者文档

#### [👨‍💻 开发者指南 (DEVELOPER_GUIDE.md)](./DEVELOPER_GUIDE.md)
**适用对象**: 扩展和贡献开发者  
**内容概要**:
- 🏗️ 架构概览和技术栈
- 📁 详细项目结构说明
- 🔧 开发环境设置
- 🧠 核心组件开发
  - Enhanced Engines 开发
  - API路由开发
  - 插件开发
- 🧪 测试开发 (单元测试、集成测试)
- 📦 构建和部署
- 🔍 调试和性能优化
- 🤝 贡献指南和代码规范

#### [🚨 故障排除指南 (TROUBLESHOOTING.md)](./TROUBLESHOOTING.md)
**适用对象**: 遇到问题的用户  
**内容概要**:
- 🔧 环境和安装问题
  - Python环境、虚拟环境、依赖安装
- 🚀 启动和运行问题
  - 服务器启动、模块导入、模型加载
- 🌐 网络和连接问题
  - Web Engine、SSL证书、API连接
- 💾 数据和存储问题
  - 数据库、缓存问题
- 🧠 AI和模型问题
  - 响应质量、性能、内存使用
- 🔍 调试和诊断工具
- 📞 获取帮助的方式

### 📋 快速参考

#### [🚀 快速开始指南 (../QUICK_START.md)](../QUICK_START.md)
**适用对象**: 新用户  
**内容概要**:
- 📋 简化的部署方案 (仅2个脚本)
- 🎯 使用方法和日常操作
- 🎪 双模式控制台命令大全
- 🌐 Web界面访问地址
- 🔧 高级功能说明
- 🚨 故障排除快速指南
- 🌟 特色功能介绍

#### [✨ 增强功能文档 (ENHANCED_FEATURES.md)](./ENHANCED_FEATURES.md)
**适用对象**: 高级用户  
**内容概要**:
- 🎯 双模式操作详解
- 🧠 Enhanced AI Engines 深度介绍
- 🎪 一行复杂操作说明
- 🌟 增强API端点
- 📊 性能和监控
- 🚀 使用案例和最佳实践

#### [📊 增强总结 (ENHANCEMENT_SUMMARY.md)](./ENHANCEMENT_SUMMARY.md)
**适用对象**: 项目管理者和技术决策者  
**内容概要**:
- ✅ 完成的增强功能总览
- 🎯 关键成就和技术亮点
- 🚀 使用示例和影响分析
- 🔮 未来增强计划

## 🎯 按使用场景选择文档

### 🆕 新用户入门
1. **[快速开始指南](../QUICK_START.md)** - 了解基本概念和安装
2. **[用户指南](./USER_GUIDE.md)** - 学习所有功能
3. **[故障排除指南](./TROUBLESHOOTING.md)** - 解决常见问题

### 💻 命令行用户
1. **[CMD/PowerShell指南](./CMD_POWERSHELL_GUIDE.md)** - 命令行集成
2. **[API参考文档](./API_REFERENCE.md)** - API调用方式
3. **[用户指南](./USER_GUIDE.md)** - 功能详解

### 🔌 API集成开发者
1. **[API参考文档](./API_REFERENCE.md)** - 完整API文档
2. **[增强功能文档](./ENHANCED_FEATURES.md)** - 高级功能
3. **[故障排除指南](./TROUBLESHOOTING.md)** - 问题解决

### 🛠️ 扩展开发者
1. **[开发者指南](./DEVELOPER_GUIDE.md)** - 开发环境和架构
2. **[API参考文档](./API_REFERENCE.md)** - API设计参考
3. **[增强总结](./ENHANCEMENT_SUMMARY.md)** - 技术实现细节

### 🚨 问题解决
1. **[故障排除指南](./TROUBLESHOOTING.md)** - 问题诊断和解决
2. **[用户指南](./USER_GUIDE.md)** - 功能使用方法
3. **[开发者指南](./DEVELOPER_GUIDE.md)** - 深度调试

## 🔍 快速查找

### 按功能查找
- **🌐 Web搜索**: [用户指南 - Web Engine](./USER_GUIDE.md#web-engine---智能网络搜索) | [CMD指南 - Web Engine](./CMD_POWERSHELL_GUIDE.md#web-engine-命令行调用)
- **🧠 代码分析**: [用户指南 - Context Engine](./USER_GUIDE.md#context-engine---代码分析) | [CMD指南 - Context Engine](./CMD_POWERSHELL_GUIDE.md#context-engine-命令行调用)
- **💾 记忆系统**: [用户指南 - Memory Engine](./USER_GUIDE.md#memory-engine---智能记忆) | [CMD指南 - Memory Engine](./CMD_POWERSHELL_GUIDE.md#memory-engine-命令行调用)
- **🎪 双模式控制台**: [用户指南 - 双模式操作](./USER_GUIDE.md#双模式操作) | [快速指南 - 控制台命令](../QUICK_START.md#双模式控制台命令)
- **🔌 API集成**: [API参考文档](./API_REFERENCE.md) | [开发者指南 - API开发](./DEVELOPER_GUIDE.md#api路由开发)

### 按问题类型查找
- **🔧 安装问题**: [故障排除 - 环境问题](./TROUBLESHOOTING.md#环境和安装问题)
- **🚀 启动问题**: [故障排除 - 启动问题](./TROUBLESHOOTING.md#启动和运行问题)
- **🌐 网络问题**: [故障排除 - 网络问题](./TROUBLESHOOTING.md#网络和连接问题)
- **🧠 AI问题**: [故障排除 - AI问题](./TROUBLESHOOTING.md#ai和模型问题)
- **💾 数据问题**: [故障排除 - 数据问题](./TROUBLESHOOTING.md#数据和存储问题)

## 📱 移动端和离线访问

### 离线文档
所有文档都是Markdown格式，可以：
- 📱 在移动设备上使用Markdown阅读器查看
- 💻 在任何文本编辑器中打开
- 🌐 转换为HTML或PDF格式
- 📚 打印为纸质文档

### 文档生成
```bash
# 生成HTML文档
pip install mkdocs mkdocs-material
mkdocs build

# 生成PDF文档
pip install markdown-pdf
markdown-pdf docs/*.md
```

## 🔄 文档更新

### 版本信息
- **文档版本**: v2.0.0
- **最后更新**: 2024-12-19
- **适用软件版本**: Reverie CLI v2.0.0+

### 更新频率
- **用户文档**: 随功能更新
- **API文档**: 随API变更
- **故障排除**: 根据用户反馈更新
- **开发者文档**: 随架构变更

## 📞 文档反馈

### 改进建议
如果您发现文档中的问题或有改进建议：

1. **GitHub Issues**: 提交文档相关的issue
2. **Pull Request**: 直接提交文档改进
3. **讨论区**: 在社区讨论区提出建议
4. **邮件反馈**: 发送详细反馈到项目邮箱

### 贡献文档
欢迎贡献文档改进：
1. Fork项目仓库
2. 编辑相关文档
3. 提交Pull Request
4. 等待审查和合并

## 🌟 文档特色

- **📱 移动友好**: 所有文档都适合移动设备阅读
- **🔍 易于搜索**: 清晰的标题和索引结构
- **💡 实用示例**: 大量实际使用示例
- **🎯 场景导向**: 按使用场景组织内容
- **🔄 持续更新**: 随软件更新保持同步
- **🌐 多语言**: 支持中英文混合阅读

---

**💡 提示**: 建议新用户从[快速开始指南](../QUICK_START.md)开始，然后根据需要查阅相应的专门文档。

**🔖 书签**: 将此页面加入书签，方便随时查找所需文档！
