"""
Tool system for Reverie CLI.

This module provides a comprehensive tool system that allows AI agents
to interact with the environment through various tools including:
- File operations (create, read, write, delete)
- Code execution in multiple languages
- Web search and information retrieval
- Git repository management
- Project management utilities
- Advanced web engine capabilities
- Context analysis and understanding
"""

from reverie_cli.tools.manager import ToolManager, get_tool_manager
from reverie_cli.tools.base import <PERSON><PERSON><PERSON>, ToolResult, ToolError
from reverie_cli.tools.registry import get_tool, list_tools, register_tool
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine

__all__ = [
    "ToolManager",
    "get_tool_manager",
    "BaseTool",
    "ToolResult",
    "ToolError",
    "get_tool",
    "list_tools",
    "register_tool",
    "WebEngine",
    "ContextEngine",
]
