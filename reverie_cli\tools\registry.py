"""
Tool registry for managing available tools.
"""

from typing import Dict, List, Optional, Type

from reverie_cli.tools.base import <PERSON>Tool, ToolInfo
from reverie_cli.tools.file_operations import (
    ReadFileTool, WriteFileTool, ListDirectoryTool, DeleteFileTool
)
from reverie_cli.tools.code_execution import PythonExecutorTool, ShellExecutorTool
from reverie_cli.tools.web_engine import WebEngine
from reverie_cli.tools.context_engine import ContextEngine
from reverie_cli.core.logging import get_logger


logger = get_logger("tool_registry")


# Global tool registry
_tool_registry: Dict[str, Type[BaseTool]] = {}
_tool_instances: Dict[str, BaseTool] = {}


def register_tool(tool_class: Type[BaseTool]) -> None:
    """
    Register a tool class.
    
    Args:
        tool_class: Tool class to register
    """
    # Create instance to get name
    instance = tool_class()
    name = instance.name
    
    _tool_registry[name] = tool_class
    _tool_instances[name] = instance
    
    logger.info(f"Registered tool: {name}")


def get_tool(name: str) -> Optional[BaseTool]:
    """
    Get a tool instance by name.
    
    Args:
        name: Tool name
        
    Returns:
        Tool instance or None if not found
    """
    return _tool_instances.get(name)


def list_tools() -> List[ToolInfo]:
    """
    List all registered tools.
    
    Returns:
        List of tool information
    """
    return [tool.get_info() for tool in _tool_instances.values()]


def get_tools_by_category(category: str) -> List[BaseTool]:
    """
    Get tools by category.
    
    Args:
        category: Tool category
        
    Returns:
        List of tools in the category
    """
    return [
        tool for tool in _tool_instances.values()
        if tool.category.value == category
    ]


def tool_exists(name: str) -> bool:
    """
    Check if a tool exists.
    
    Args:
        name: Tool name
        
    Returns:
        True if tool exists, False otherwise
    """
    return name in _tool_instances


def unregister_tool(name: str) -> bool:
    """
    Unregister a tool.
    
    Args:
        name: Tool name
        
    Returns:
        True if tool was unregistered, False if not found
    """
    if name in _tool_registry:
        del _tool_registry[name]
        del _tool_instances[name]
        logger.info(f"Unregistered tool: {name}")
        return True
    return False


def initialize_default_tools():
    """Initialize and register default tools."""
    logger.info("Initializing default tools")
    
    # File operation tools
    register_tool(ReadFileTool)
    register_tool(WriteFileTool)
    register_tool(ListDirectoryTool)
    register_tool(DeleteFileTool)
    
    # Code execution tools
    register_tool(PythonExecutorTool)
    register_tool(ShellExecutorTool)

    # Advanced engines
    register_tool(WebEngine)
    register_tool(ContextEngine)

    logger.info(f"Initialized {len(_tool_instances)} default tools")


def get_registry_stats() -> Dict[str, int]:
    """
    Get registry statistics.
    
    Returns:
        Dictionary with registry statistics
    """
    from collections import Counter
    
    categories = Counter(tool.category.value for tool in _tool_instances.values())
    
    return {
        "total_tools": len(_tool_instances),
        "categories": dict(categories)
    }


# Initialize default tools on import (can be disabled by setting environment variable)
import os
if os.getenv("REVERIE_SKIP_TOOL_INIT") != "1":
    initialize_default_tools()
