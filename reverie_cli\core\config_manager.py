"""
Advanced configuration management for Reverie CLI.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from datetime import datetime

from reverie_cli.core.logging import get_logger
from reverie_cli.core.config import get_settings, Settings


class ConfigManager:
    """
    Advanced configuration management system.
    
    Provides configuration validation, environment-specific configs,
    and runtime configuration updates.
    """
    
    def __init__(self):
        self.logger = get_logger("config_manager")
        self.settings = get_settings()
        self.config_history: list = []
        
        # Configuration file paths
        self.config_dir = Path.home() / ".reverie_cli"
        self.config_file = self.config_dir / "config.yaml"
        self.env_config_file = self.config_dir / f"config.{self.settings.environment}.yaml"
        
        self.logger.info("ConfigManager initialized")
    
    def load_config_file(self, config_path: Optional[Path] = None) -> Dict[str, Any]:
        """Load configuration from file."""
        if config_path is None:
            config_path = self.config_file
        
        if not config_path.exists():
            self.logger.warning(f"Config file not found: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    config = json.load(f)
                else:
                    self.logger.error(f"Unsupported config file format: {config_path}")
                    return {}
            
            self.logger.info(f"Loaded config from: {config_path}")
            return config or {}
            
        except Exception as e:
            self.logger.error(f"Failed to load config file {config_path}: {e}")
            return {}
    
    def save_config_file(self, config: Dict[str, Any], config_path: Optional[Path] = None):
        """Save configuration to file."""
        if config_path is None:
            config_path = self.config_file
        
        # Ensure config directory exists
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                elif config_path.suffix.lower() == '.json':
                    json.dump(config, f, indent=2)
                else:
                    self.logger.error(f"Unsupported config file format: {config_path}")
                    return
            
            self.logger.info(f"Saved config to: {config_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save config file {config_path}: {e}")
    
    def get_effective_config(self) -> Dict[str, Any]:
        """Get effective configuration from all sources."""
        # Start with default config
        config = {}
        
        # Load base config file
        base_config = self.load_config_file(self.config_file)
        config.update(base_config)
        
        # Load environment-specific config
        env_config = self.load_config_file(self.env_config_file)
        config.update(env_config)
        
        # Override with environment variables
        env_overrides = self._get_env_overrides()
        config.update(env_overrides)
        
        return config
    
    def _get_env_overrides(self) -> Dict[str, Any]:
        """Get configuration overrides from environment variables."""
        overrides = {}
        
        # Define environment variable mappings
        env_mappings = {
            'REVERIE_LOG_LEVEL': 'logging.level',
            'REVERIE_SERVER_HOST': 'server.host',
            'REVERIE_SERVER_PORT': 'server.port',
            'REVERIE_MODEL_DEFAULT': 'model.default_model',
            'REVERIE_MODEL_DEVICE': 'model.device',
            'REVERIE_AGENT_MAX_ITERATIONS': 'agent.max_iterations',
            'REVERIE_AGENT_TIMEOUT': 'agent.timeout_seconds',
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert value to appropriate type
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif value.isdigit():
                    value = int(value)
                elif self._is_float(value):
                    value = float(value)
                
                # Set nested config value
                self._set_nested_value(overrides, config_path, value)
        
        return overrides
    
    def _is_float(self, value: str) -> bool:
        """Check if string represents a float."""
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    def _set_nested_value(self, config: Dict[str, Any], path: str, value: Any):
        """Set nested configuration value using dot notation."""
        keys = path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, list[str]]:
        """Validate configuration."""
        errors = []
        
        # Validate server configuration
        if 'server' in config:
            server_config = config['server']
            if 'port' in server_config:
                port = server_config['port']
                if not isinstance(port, int) or port < 1 or port > 65535:
                    errors.append("Server port must be between 1 and 65535")
        
        # Validate model configuration
        if 'model' in config:
            model_config = config['model']
            if 'max_tokens' in model_config:
                max_tokens = model_config['max_tokens']
                if not isinstance(max_tokens, int) or max_tokens < 1:
                    errors.append("Model max_tokens must be a positive integer")
            
            if 'temperature' in model_config:
                temperature = model_config['temperature']
                if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
                    errors.append("Model temperature must be between 0 and 2")
        
        # Validate agent configuration
        if 'agent' in config:
            agent_config = config['agent']
            if 'max_iterations' in agent_config:
                max_iterations = agent_config['max_iterations']
                if not isinstance(max_iterations, int) or max_iterations < 1:
                    errors.append("Agent max_iterations must be a positive integer")
        
        return len(errors) == 0, errors
    
    def update_config(self, updates: Dict[str, Any], persist: bool = True) -> bool:
        """Update configuration at runtime."""
        try:
            # Record current config for history
            current_config = self.settings.dict()
            self.config_history.append({
                "timestamp": datetime.now().isoformat(),
                "config": current_config.copy()
            })
            
            # Validate updates
            test_config = current_config.copy()
            test_config.update(updates)
            
            is_valid, errors = self.validate_config(test_config)
            if not is_valid:
                self.logger.error(f"Config validation failed: {errors}")
                return False
            
            # Apply updates
            for key, value in updates.items():
                if hasattr(self.settings, key):
                    setattr(self.settings, key, value)
                    self.logger.info(f"Updated config: {key} = {value}")
            
            # Persist to file if requested
            if persist:
                self.save_config_file(test_config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update config: {e}")
            return False
    
    def reset_config(self):
        """Reset configuration to defaults."""
        try:
            # Create new settings instance with defaults
            default_settings = Settings()
            
            # Update current settings
            for field_name, field_value in default_settings.dict().items():
                setattr(self.settings, field_name, field_value)
            
            self.logger.info("Configuration reset to defaults")
            
        except Exception as e:
            self.logger.error(f"Failed to reset config: {e}")
    
    def export_config(self, format: str = 'yaml') -> str:
        """Export current configuration."""
        config = self.settings.dict()
        
        if format.lower() == 'yaml':
            return yaml.dump(config, default_flow_style=False, indent=2)
        elif format.lower() == 'json':
            return json.dumps(config, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def get_config_info(self) -> Dict[str, Any]:
        """Get configuration information."""
        return {
            "config_dir": str(self.config_dir),
            "config_file": str(self.config_file),
            "env_config_file": str(self.env_config_file),
            "environment": self.settings.environment,
            "config_history_count": len(self.config_history),
            "last_update": self.config_history[-1]["timestamp"] if self.config_history else None
        }


# Global config manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global config manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
