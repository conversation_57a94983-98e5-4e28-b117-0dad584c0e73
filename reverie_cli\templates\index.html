<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reverie CLI - AI-Native Development Server</title>
    <link rel="icon" type="image/png" href="/resources/icons/RCS.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
        }
        
        .chat-message {
            animation: fadeInUp 0.3s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="gradient-bg shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/resources/icons/RCS.png" alt="Reverie CLI" class="w-10 h-10">
                    <div>
                        <h1 class="text-2xl font-bold">Reverie CLI</h1>
                        <p class="text-sm opacity-80">AI-Native Development Server v{{ version }}</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm">Server Online</span>
                    </div>
                    
                    <button onclick="toggleTheme()" class="p-2 rounded-lg glass-effect hover:bg-white hover:bg-opacity-20 transition-all">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Hero Section -->
        <section class="text-center mb-12">
            <h2 class="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                The Future of AI-Powered Development
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Reverie CLI brings together the power of modern AI with an intuitive development experience. 
                Code, chat, and create with intelligent assistance.
            </p>
            
            <div class="flex justify-center space-x-4">
                <button onclick="openChat()" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105">
                    🤖 Start Coding
                </button>
                <a href="/docs" class="bg-gray-700 hover:bg-gray-600 px-6 py-3 rounded-lg font-semibold transition-all">
                    📚 API Docs
                </a>
            </div>
        </section>

        <!-- Features Grid -->
        <section class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <!-- AI Models -->
            <div class="glass-effect rounded-xl p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                        🧠
                    </div>
                    <h3 class="text-xl font-semibold">AI Models</h3>
                </div>
                <p class="text-gray-300 mb-4">Support for multiple AI backends including Lucy-128k, Llama 2, and Mistral.</p>
                <button onclick="showModels()" class="text-blue-400 hover:text-blue-300 font-medium">
                    Manage Models →
                </button>
            </div>

            <!-- Interactive Console -->
            <div class="glass-effect rounded-xl p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                        💻
                    </div>
                    <h3 class="text-xl font-semibold">Interactive Console</h3>
                </div>
                <p class="text-gray-300 mb-4">Command-line interface with real-time AI assistance and tool integration.</p>
                <button onclick="showConsole()" class="text-green-400 hover:text-green-300 font-medium">
                    Open Console →
                </button>
            </div>

            <!-- Agent System -->
            <div class="glass-effect rounded-xl p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                        🤖
                    </div>
                    <h3 class="text-xl font-semibold">AI Agents</h3>
                </div>
                <p class="text-gray-300 mb-4">Intelligent agents for code analysis, generation, and project management.</p>
                <button onclick="showAgents()" class="text-purple-400 hover:text-purple-300 font-medium">
                    View Agents →
                </button>
            </div>
        </section>

        <!-- Enhanced Chat Interface -->
        <section id="chat-section" class="hidden">
            <div class="glass-effect rounded-xl p-6 max-w-6xl mx-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-semibold">🤖 AI Development Assistant</h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-sm text-gray-300">Connected</span>
                        </div>
                        <button onclick="clearChat()" class="text-gray-400 hover:text-white transition-colors">
                            🗑️ Clear
                        </button>
                        <button onclick="closeChat()" class="text-gray-400 hover:text-white transition-colors">
                            ✕
                        </button>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div id="chat-messages" class="h-96 overflow-y-auto mb-4 p-4 bg-gray-800 rounded-lg space-y-4">
                    <div class="chat-message">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm">
                                🤖
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-700 rounded-lg p-3">
                                    <p class="text-sm text-gray-300">Hello! I'm your AI development assistant. I can help you with:</p>
                                    <ul class="mt-2 text-sm text-gray-300 list-disc list-inside">
                                        <li>🔍 Web search and research</li>
                                        <li>📊 Code analysis and understanding</li>
                                        <li>✍️ Code generation and refactoring</li>
                                        <li>🐛 Debugging and troubleshooting</li>
                                        <li>📚 Documentation and explanations</li>
                                    </ul>
                                    <p class="mt-2 text-sm text-gray-300">What would you like to work on?</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Input Area -->
                <div class="space-y-4">
                    <!-- Context Bar -->
                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                        <div class="flex items-center space-x-2">
                            <span>📁</span>
                            <span id="current-project">No project selected</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>🔧</span>
                            <span id="active-tools">Web Engine, Context Engine</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>🧠</span>
                            <span id="model-status">Lucy-128k Ready</span>
                        </div>
                    </div>

                    <!-- Input Form -->
                    <form onsubmit="sendMessage(event)" class="flex space-x-4">
                        <div class="flex-1 relative">
                            <textarea
                                id="chat-input"
                                placeholder="Ask me anything about your code, or request help with development tasks..."
                                class="w-full p-4 bg-gray-800 border border-gray-600 rounded-lg resize-none focus:outline-none focus:border-blue-500 transition-colors"
                                rows="3"
                                onkeydown="handleInputKeydown(event)"
                            ></textarea>
                            <div class="absolute bottom-2 right-2 flex space-x-2">
                                <button type="button" onclick="attachFile()" class="text-gray-400 hover:text-white transition-colors" title="Attach file">
                                    📎
                                </button>
                                <button type="button" onclick="toggleVoice()" class="text-gray-400 hover:text-white transition-colors" title="Voice input">
                                    🎤
                                </button>
                            </div>
                        </div>
                        <button
                            type="submit"
                            class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                            id="send-button"
                        >
                            Send
                        </button>
                    </form>

                    <!-- Quick Actions -->
                    <div class="flex flex-wrap gap-2">
                        <button onclick="quickAction('analyze-project')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                            📊 Analyze Project
                        </button>
                        <button onclick="quickAction('search-web')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                            🌐 Search Web
                        </button>
                        <button onclick="quickAction('generate-code')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                            ✍️ Generate Code
                        </button>
                        <button onclick="quickAction('review-code')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                            🔍 Review Code
                        </button>
                        <button onclick="quickAction('explain-concept')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                            💡 Explain Concept
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Code Editor Section -->
        <section id="editor-section" class="hidden mt-8">
            <div class="glass-effect rounded-xl p-6 max-w-6xl mx-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-semibold">📝 Code Editor</h3>
                    <div class="flex items-center space-x-4">
                        <select id="language-select" class="bg-gray-800 border border-gray-600 rounded px-3 py-1 text-sm">
                            <option value="python">Python</option>
                            <option value="javascript">JavaScript</option>
                            <option value="typescript">TypeScript</option>
                            <option value="rust">Rust</option>
                            <option value="go">Go</option>
                            <option value="java">Java</option>
                        </select>
                        <button onclick="runCode()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm transition-colors">
                            ▶️ Run
                        </button>
                        <button onclick="saveCode()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm transition-colors">
                            💾 Save
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Code Input -->
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <label class="text-sm font-medium text-gray-300">Code</label>
                            <div class="flex space-x-2">
                                <button onclick="formatCode()" class="text-xs text-gray-400 hover:text-white transition-colors">
                                    Format
                                </button>
                                <button onclick="analyzeCode()" class="text-xs text-gray-400 hover:text-white transition-colors">
                                    Analyze
                                </button>
                            </div>
                        </div>
                        <textarea
                            id="code-editor"
                            class="w-full h-80 p-4 bg-gray-800 border border-gray-600 rounded-lg font-mono text-sm resize-none focus:outline-none focus:border-blue-500 transition-colors"
                            placeholder="# Write your code here..."
                        ></textarea>
                    </div>

                    <!-- Output -->
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <label class="text-sm font-medium text-gray-300">Output</label>
                            <button onclick="clearOutput()" class="text-xs text-gray-400 hover:text-white transition-colors">
                                Clear
                            </button>
                        </div>
                        <div id="code-output" class="w-full h-80 p-4 bg-gray-900 border border-gray-600 rounded-lg font-mono text-sm overflow-y-auto">
                            <div class="text-gray-500">Output will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Project Explorer -->
        <section id="explorer-section" class="hidden mt-8">
            <div class="glass-effect rounded-xl p-6 max-w-6xl mx-auto">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-semibold">📁 Project Explorer</h3>
                    <div class="flex items-center space-x-4">
                        <button onclick="refreshProject()" class="text-gray-400 hover:text-white transition-colors">
                            🔄 Refresh
                        </button>
                        <button onclick="analyzeProject()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm transition-colors">
                            🔍 Analyze
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- File Tree -->
                    <div class="lg:col-span-1">
                        <h4 class="text-lg font-medium mb-4">File Structure</h4>
                        <div id="file-tree" class="bg-gray-800 rounded-lg p-4 h-96 overflow-y-auto">
                            <div class="text-gray-500">Select a project to explore...</div>
                        </div>
                    </div>

                    <!-- Project Info -->
                    <div class="lg:col-span-2">
                        <h4 class="text-lg font-medium mb-4">Project Information</h4>
                        <div id="project-info" class="bg-gray-800 rounded-lg p-4 h-96 overflow-y-auto">
                            <div class="text-gray-500">Project details will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
                    <button onclick="closeChat()" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <!-- Chat Messages -->
                <div id="chat-messages" class="h-96 overflow-y-auto mb-4 p-4 bg-gray-800 rounded-lg">
                    <div class="chat-message mb-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                🤖
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-400 mb-1">AI Assistant</p>
                                <p class="text-white">Hello! I'm your AI coding assistant. How can I help you today?</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="flex space-x-3">
                    <input 
                        type="text" 
                        id="chat-input" 
                        placeholder="Ask me anything about coding..."
                        class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500"
                        onkeypress="handleChatKeyPress(event)"
                    >
                    <button 
                        onclick="sendMessage()" 
                        class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg font-semibold transition-all"
                    >
                        Send
                    </button>
                </div>
            </div>
        </section>

        <!-- System Status -->
        <section class="mt-12">
            <div class="glass-effect rounded-xl p-6">
                <h3 class="text-xl font-semibold mb-4">System Status</h3>
                <div class="grid md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-400" id="server-status">Online</div>
                        <div class="text-sm text-gray-400">Server</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-400" id="model-status">Ready</div>
                        <div class="text-sm text-gray-400">AI Model</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-400" id="agent-status">Active</div>
                        <div class="text-sm text-gray-400">Agents</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-400" id="memory-usage">--</div>
                        <div class="text-sm text-gray-400">Memory</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="mt-16 py-8 border-t border-gray-700">
        <div class="container mx-auto px-6 text-center text-gray-400">
            <p>&copy; 2025 Rilance Code Studio. Built with ❤️ and AI.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
</body>
</html>
