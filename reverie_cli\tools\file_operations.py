"""
File operation tools.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any, List

from reverie_cli.tools.base import BaseTool, ToolResult, ToolParameter, ToolCategory
from reverie_cli.core.exceptions import FileOperationError


class ReadFileTool(BaseTool):
    """Tool for reading file contents."""
    
    @property
    def name(self) -> str:
        return "read_file"
    
    @property
    def description(self) -> str:
        return "Read the contents of a file"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.FILE_SYSTEM
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="path",
                type="string",
                description="Path to the file to read",
                required=True
            ),
            ToolParameter(
                name="encoding",
                type="string",
                description="File encoding",
                required=False,
                default="utf-8"
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute the read file operation."""
        path = Path(parameters["path"])
        encoding = parameters.get("encoding", "utf-8")
        
        try:
            if not path.exists():
                return ToolResult(
                    success=False,
                    error="File not found",
                    message=f"File does not exist: {path}"
                )
            
            if not path.is_file():
                return ToolResult(
                    success=False,
                    error="Not a file",
                    message=f"Path is not a file: {path}"
                )
            
            content = path.read_text(encoding=encoding)
            
            return ToolResult(
                success=True,
                result=content,
                message=f"Successfully read file: {path}",
                metadata={
                    "path": str(path),
                    "size": len(content),
                    "encoding": encoding
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Failed to read file: {path}"
            )


class WriteFileTool(BaseTool):
    """Tool for writing file contents."""
    
    @property
    def name(self) -> str:
        return "write_file"
    
    @property
    def description(self) -> str:
        return "Write content to a file"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.FILE_SYSTEM
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="path",
                type="string",
                description="Path to the file to write",
                required=True
            ),
            ToolParameter(
                name="content",
                type="string",
                description="Content to write to the file",
                required=True
            ),
            ToolParameter(
                name="encoding",
                type="string",
                description="File encoding",
                required=False,
                default="utf-8"
            ),
            ToolParameter(
                name="create_dirs",
                type="boolean",
                description="Create parent directories if they don't exist",
                required=False,
                default=True
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute the write file operation."""
        path = Path(parameters["path"])
        content = parameters["content"]
        encoding = parameters.get("encoding", "utf-8")
        create_dirs = parameters.get("create_dirs", True)
        
        try:
            # Create parent directories if needed
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            path.write_text(content, encoding=encoding)
            
            return ToolResult(
                success=True,
                result=str(path),
                message=f"Successfully wrote file: {path}",
                metadata={
                    "path": str(path),
                    "size": len(content),
                    "encoding": encoding
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Failed to write file: {path}"
            )


class ListDirectoryTool(BaseTool):
    """Tool for listing directory contents."""
    
    @property
    def name(self) -> str:
        return "list_directory"
    
    @property
    def description(self) -> str:
        return "List the contents of a directory"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.FILE_SYSTEM
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="path",
                type="string",
                description="Path to the directory to list",
                required=True
            ),
            ToolParameter(
                name="recursive",
                type="boolean",
                description="List recursively",
                required=False,
                default=False
            ),
            ToolParameter(
                name="include_hidden",
                type="boolean",
                description="Include hidden files",
                required=False,
                default=False
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute the list directory operation."""
        path = Path(parameters["path"])
        recursive = parameters.get("recursive", False)
        include_hidden = parameters.get("include_hidden", False)
        
        try:
            if not path.exists():
                return ToolResult(
                    success=False,
                    error="Directory not found",
                    message=f"Directory does not exist: {path}"
                )
            
            if not path.is_dir():
                return ToolResult(
                    success=False,
                    error="Not a directory",
                    message=f"Path is not a directory: {path}"
                )
            
            items = []
            
            if recursive:
                pattern = "**/*" if include_hidden else "**/[!.]*"
                for item in path.glob(pattern):
                    items.append({
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else None
                    })
            else:
                for item in path.iterdir():
                    if not include_hidden and item.name.startswith('.'):
                        continue
                    
                    items.append({
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else None
                    })
            
            return ToolResult(
                success=True,
                result=items,
                message=f"Successfully listed directory: {path}",
                metadata={
                    "path": str(path),
                    "count": len(items),
                    "recursive": recursive
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Failed to list directory: {path}"
            )


class DeleteFileTool(BaseTool):
    """Tool for deleting files and directories."""
    
    @property
    def name(self) -> str:
        return "delete_file"
    
    @property
    def description(self) -> str:
        return "Delete a file or directory"
    
    @property
    def category(self) -> ToolCategory:
        return ToolCategory.FILE_SYSTEM
    
    @property
    def parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="path",
                type="string",
                description="Path to the file or directory to delete",
                required=True
            ),
            ToolParameter(
                name="recursive",
                type="boolean",
                description="Delete directories recursively",
                required=False,
                default=False
            )
        ]
    
    async def execute(self, parameters: Dict[str, Any]) -> ToolResult:
        """Execute the delete operation."""
        path = Path(parameters["path"])
        recursive = parameters.get("recursive", False)
        
        try:
            if not path.exists():
                return ToolResult(
                    success=False,
                    error="Path not found",
                    message=f"Path does not exist: {path}"
                )
            
            if path.is_file():
                path.unlink()
                return ToolResult(
                    success=True,
                    result=str(path),
                    message=f"Successfully deleted file: {path}"
                )
            elif path.is_dir():
                if recursive:
                    shutil.rmtree(path)
                    return ToolResult(
                        success=True,
                        result=str(path),
                        message=f"Successfully deleted directory: {path}"
                    )
                else:
                    path.rmdir()  # Only works if empty
                    return ToolResult(
                        success=True,
                        result=str(path),
                        message=f"Successfully deleted empty directory: {path}"
                    )
            else:
                return ToolResult(
                    success=False,
                    error="Unknown path type",
                    message=f"Cannot delete path: {path}"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                message=f"Failed to delete path: {path}"
            )
